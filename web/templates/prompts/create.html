{% extends "base.html" %}

{% block title %}Create Prompt | GPT Sir{% endblock %}

{% block content %}
<div class="prompt-form-container">
  <div class="prompt-form-header">
    <h1>Create New Prompt</h1>
    <a href="/prompts/" class="back-btn">Back to Prompts</a>
  </div>

  {% if error %}
    <div class="error-message">
      {{ error }}
    </div>
  {% endif %}

  <form method="post" class="prompt-form">
    <div class="form-group">
      <label for="promptLabel">Prompt Label *</label>
      <input type="text" id="promptLabel" name="promptLabel" required value="{{ promptLabel or '' }}">
      <div class="field-hint">A short, descriptive label for the prompt (will be converted to UPPERCASE with spaces replaced by underscores)</div>
    </div>

    <div class="form-group">
      <label for="promptCategory">Prompt Category *</label>
      <select id="promptCategory" name="promptCategory" required>
        <option value="">Select a category</option>
        <option value="ibookgpt" {% if promptCategory == 'ibookgpt' %}selected{% endif %}>ibookgpt</option>
        <option value="gptsir" {% if promptCategory == 'gptsir' %}selected{% endif %}>gptsir</option>
      </select>
    </div>

    <div class="form-group">
      <label for="prompt">Prompt *</label>
      <textarea id="prompt" name="prompt" rows="8" required>{{ prompt or '' }}</textarea>
    </div>

    <div class="form-group">
      <label for="promptDescription">Prompt Description</label>
      <textarea id="promptDescription" name="promptDescription" rows="4">{{ promptDescription or '' }}</textarea>
      <div class="field-hint">Optional description or additional information about the prompt</div>
    </div>

    <div class="form-actions">
      <button type="submit" class="submit-btn">Create Prompt</button>
    </div>
  </form>
</div>
{% endblock %}

{% block head %}
<style>
  .prompt-form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
  }

  .prompt-form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .back-btn {
    background-color: var(--beige);
    color: var(--gunmetal);
    padding: 8px 15px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s;
  }

  .back-btn:hover {
    background-color: #e0dbc5;
  }

  .error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border-left: 4px solid #c62828;
  }

  .prompt-form {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .form-group {
    margin-bottom: 20px;
  }

  .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--gunmetal);
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
    font-size: 1rem;
  }

  .form-group textarea {
    resize: vertical;
  }

  .field-hint {
    font-size: 0.85rem;
    color: #666;
    margin-top: 5px;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
  }

  .submit-btn {
    background-color: var(--cerise);
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .submit-btn:hover {
    background-color: var(--bittersweet);
  }

  @media (max-width: 768px) {
    .prompt-form-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
  }
</style>
{% endblock %}

{% block scripts %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.prompt-form');
    const promptLabelInput = document.getElementById('promptLabel');
    const promptLabelPreview = document.createElement('div');

    // Add the preview element
    promptLabelPreview.className = 'label-preview';
    promptLabelInput.parentNode.insertBefore(promptLabelPreview, promptLabelInput.nextSibling.nextSibling);

    // Update the preview when the input changes
    promptLabelInput.addEventListener('input', function() {
      const formattedLabel = this.value.trim().toUpperCase().replace(/ /g, '_');
      promptLabelPreview.textContent = formattedLabel ? `Preview: ${formattedLabel}` : '';
    });

    // Trigger the input event to show the initial preview
    const event = new Event('input');
    promptLabelInput.dispatchEvent(event);

    form.addEventListener('submit', function(e) {
      const promptLabel = promptLabelInput.value.trim();
      const promptCategory = document.getElementById('promptCategory').value;
      const prompt = document.getElementById('prompt').value.trim();

      if (!promptLabel || !promptCategory || !prompt) {
        e.preventDefault();
        alert('Please fill in all required fields.');
      }
    });
  });
</script>

<style>
  .label-preview {
    font-size: 0.85rem;
    color: #666;
    margin-top: 5px;
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 3px 6px;
    border-radius: 3px;
    display: inline-block;
  }
</style>
{% endblock %}
