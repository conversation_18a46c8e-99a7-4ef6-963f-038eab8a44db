{% extends "base.html" %}

{% block title %}View Prompt | GPT Sir{% endblock %}

{% block content %}
<div class="prompt-view-container">
  <div class="prompt-view-header">
    <h1>View Prompt</h1>
    <div class="header-actions">
      <a href="/prompts/edit/{{ prompt.id }}" class="edit-btn">Edit</a>
      <a href="/prompts/" class="back-btn">Back to Prompts</a>
    </div>
  </div>

  <div class="prompt-view-card">
    <div class="prompt-meta">
      <div class="prompt-label">{{ prompt.prompt_label }}</div>
      <div class="prompt-category">{{ prompt.prompt_category }}</div>
    </div>

    <div class="prompt-section">
      <h3>Prompt</h3>
      <div class="prompt-content">{{ prompt.prompt }}</div>
    </div>

    {% if prompt.prompt_description %}
      <div class="prompt-section">
        <h3>Description</h3>
        <div class="prompt-description">{{ prompt.prompt_description }}</div>
      </div>
    {% endif %}

    <!-- Timestamps removed as they don't exist in the database -->
  </div>
</div>
{% endblock %}

{% block head %}
<style>
  .prompt-view-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
  }

  .prompt-view-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .header-actions {
    display: flex;
    gap: 10px;
  }

  .edit-btn {
    background-color: var(--tea-green);
    color: var(--gunmetal);
    padding: 8px 15px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s;
  }

  .edit-btn:hover {
    background-color: #b5e0c0;
  }

  .back-btn {
    background-color: var(--beige);
    color: var(--gunmetal);
    padding: 8px 15px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s;
  }

  .back-btn:hover {
    background-color: #e0dbc5;
  }

  .prompt-view-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .prompt-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
  }

  .prompt-label {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gunmetal);
  }

  .prompt-category {
    display: inline-block;
    background-color: var(--tea-green);
    color: var(--gunmetal);
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 0.9rem;
  }

  .prompt-section {
    margin-bottom: 20px;
  }

  .prompt-section h3 {
    margin-bottom: 10px;
    color: var(--gunmetal);
    font-size: 1.1rem;
  }

  .prompt-content {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
    white-space: pre-wrap;
    font-family: monospace;
    line-height: 1.5;
  }

  .prompt-description {
    line-height: 1.5;
    color: #555;
  }

  .prompt-timestamps {
    margin-top: 30px;
    padding-top: 15px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    color: #777;
    font-size: 0.9rem;
  }

  @media (max-width: 768px) {
    .prompt-view-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }

    .prompt-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }

    .prompt-timestamps {
      flex-direction: column;
      gap: 5px;
    }
  }
</style>
{% endblock %}
