{% extends "base.html" %}

{% block title %}GPT Prompts | GPT Sir{% endblock %}

{% block content %}
<div class="prompts-container">
  <div class="prompts-header">
    <h1>GPT Prompts</h1>
    <a href="/prompts/create" class="create-btn">Create New Prompt</a>
  </div>

  {% if error %}
    <div class="error-message">
      {{ error }}
    </div>
  {% endif %}

  {% if prompts %}
    <div class="prompts-list">
      {% for prompt in prompts %}
        <div class="prompt-item">
          <div class="prompt-content">
            <h3 class="prompt-label">{{ prompt.prompt_label }}</h3>
            <div class="prompt-category">{{ prompt.prompt_category }}</div>
            <div class="prompt-text">{{ prompt.prompt[:100] }}{% if prompt.prompt|length > 100 %}...{% endif %}</div>
          </div>
          <div class="prompt-actions">
            <a href="/prompts/view/{{ prompt.id }}" class="action-btn view-btn">View</a>
            <a href="/prompts/edit/{{ prompt.id }}" class="action-btn edit-btn">Edit</a>
          </div>
        </div>
      {% endfor %}
    </div>
  {% else %}
    <div class="no-prompts">
      <p>No prompts found. <a href="/prompts/create">Create your first prompt</a>.</p>
    </div>
  {% endif %}
</div>
{% endblock %}

{% block head %}
<style>
  .prompts-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
  }

  .prompts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .create-btn {
    background-color: var(--cerise);
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s;
  }

  .create-btn:hover {
    background-color: var(--bittersweet);
  }

  .prompts-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .prompt-item {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .prompt-content {
    flex: 1;
  }

  .prompt-label {
    margin: 0 0 5px 0;
    font-size: 1.2rem;
    color: var(--gunmetal);
  }

  .prompt-category {
    display: inline-block;
    background-color: var(--tea-green);
    color: var(--gunmetal);
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-bottom: 8px;
  }

  .prompt-text {
    color: #555;
    font-size: 0.95rem;
    line-height: 1.4;
  }

  .prompt-actions {
    display: flex;
    gap: 10px;
  }

  .action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: background-color 0.3s;
  }

  .view-btn {
    background-color: var(--tea-green);
    color: var(--gunmetal);
  }

  .view-btn:hover {
    background-color: #b5e0c0;
  }

  .edit-btn {
    background-color: var(--beige);
    color: var(--gunmetal);
  }

  .edit-btn:hover {
    background-color: #e0dbc5;
  }

  .no-prompts {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    text-align: center;
  }

  .error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #c62828;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: 768px) {
    .prompt-item {
      flex-direction: column;
      align-items: flex-start;
    }

    .prompt-actions {
      margin-top: 10px;
      align-self: flex-end;
    }
  }
</style>
{% endblock %}
