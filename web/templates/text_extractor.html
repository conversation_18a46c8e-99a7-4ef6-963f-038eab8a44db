{% extends "base.html" %}

{% block title %}Text Extractor | GPT Sir{% endblock %}

{% block content %}
<div class="text-extractor-container">
  <h1>Text Extractor</h1>
  <p class="description">Extract text from PDF documents using AI vision models.</p>

  <div class="extractor-card">
    <form id="extractorForm" class="extractor-form">
      <div class="form-group">
        <label for="resId">Resource ID:</label>
        <input type="text" id="resId" name="resId" required placeholder="Enter resource ID">
      </div>
      <button type="submit" class="submit-btn">Extract Text</button>
    </form>
  </div>

  <div id="loadingIndicator" class="loading-indicator" style="display: none;">
    <div class="spinner"></div>
    <p>Processing... This may take a few minutes.</p>
    <div id="progressContainer" class="progress-container">
      <div id="progressBar" class="progress-bar"></div>
      <div id="progressText" class="progress-text">0%</div>
    </div>
  </div>

  <div id="resultContainer" class="result-container" style="display: none;">
    <div class="result-header">
      <h2>Extraction Results</h2>
      <div class="cost-info">
        <span>Total LLM Cost: </span>
        <span id="costValue">₹0.00</span>
      </div>
    </div>
    <div class="result-content">
      <pre id="extractedText" class="extracted-text"></pre>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('extractorForm');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const resultContainer = document.getElementById('resultContainer');
    const extractedText = document.getElementById('extractedText');
    const costValue = document.getElementById('costValue');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');

    form.addEventListener('submit', async function(e) {
      e.preventDefault();
      
      const resId = document.getElementById('resId').value.trim();
      if (!resId) {
        alert('Please enter a valid Resource ID');
        return;
      }

      // Show loading indicator
      loadingIndicator.style.display = 'flex';
      resultContainer.style.display = 'none';
      
      try {
        // Start the extraction process
        const startResponse = await fetch('/api/text-extractor', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ resId }),
        });
        
        if (!startResponse.ok) {
          throw new Error('Failed to start extraction process');
        }
        
        const startData = await startResponse.json();
        const taskId = startData.task_id;
        
        // Poll for progress updates
        const pollInterval = setInterval(async () => {
          const statusResponse = await fetch(`/api/task/${taskId}/status`);
          if (!statusResponse.ok) {
            clearInterval(pollInterval);
            throw new Error('Failed to get task status');
          }
          
          const statusData = await statusResponse.json();
          
          // Update progress bar
          if (statusData.progress) {
            progressBar.style.width = `${statusData.progress}%`;
            progressText.textContent = `${statusData.progress}%`;
          }
          
          // Check if task is complete
          if (statusData.status === 'completed') {
            clearInterval(pollInterval);
            
            // Get the result
            const resultResponse = await fetch(`/api/sse/${taskId}/result`);
            if (!resultResponse.ok) {
              throw new Error('Failed to get extraction result');
            }
            
            const resultData = await resultResponse.json();
            
            // Display the result
            extractedText.textContent = resultData.extracted_text || 'No text extracted';
            costValue.textContent = `₹${resultData.cost.toFixed(2)}`;
            
            // Hide loading indicator and show result
            loadingIndicator.style.display = 'none';
            resultContainer.style.display = 'block';
          } else if (statusData.status === 'failed') {
            clearInterval(pollInterval);
            throw new Error(statusData.error || 'Extraction failed');
          }
        }, 2000);
        
      } catch (error) {
        console.error('Error:', error);
        alert(`Error: ${error.message}`);
        loadingIndicator.style.display = 'none';
      }
    });
  });
</script>
{% endblock %}
