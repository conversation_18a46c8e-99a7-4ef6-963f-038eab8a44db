{% extends "base.html" %}

{% block title %}Home | GPT Sir{% endblock %}

{% block content %}
<div class="home-container">
  <h1>Welcome to GPT Sir</h1>

  <div class="project-grid" style="grid-template-columns: repeat(3, 1fr);">
    <!-- Question Paper Extraction Card -->
    {% if "ROLE_GPT_MANAGER" in request.session.get("roles", []) %}
      <a href="/upload" class="project-item">
        <div class="project-item-content">
          <div class="project-item-icon">📚</div>
          <h3 class="project-item-title">Question Paper Extraction</h3>
          <p>Extract and process content from Question Papers.</p>
        </div>
      </a>
    {% else %}
      <div class="project-item disabled" onclick="showPermissionMessage()">
        <div class="project-item-content">
          <div class="project-item-icon">📚</div>
          <h3 class="project-item-title">Question Paper Extraction</h3>
          <p>Extract and process content from Question Papers.</p>
        </div>
      </div>
    {% endif %}

    <!-- VideoGeneration Card -->
    {% if "ROLE_GPT_MANAGER" in request.session.get("roles", []) %}
      <a href="/video" class="project-item">
        <div class="project-item-content">
          <div class="project-item-icon">🎬</div>
          <h3 class="project-item-title">VideoGeneration</h3>
          <p>Generate scroll videos from your content.</p>
        </div>
      </a>
    {% else %}
      <div class="project-item disabled" onclick="showPermissionMessage()">
        <div class="project-item-content">
          <div class="project-item-icon">🎬</div>
          <h3 class="project-item-title">VideoGeneration</h3>
          <p>Generate scroll videos from your content.</p>
        </div>
      </div>
    {% endif %}


    <!-- GPT Prompts Card -->
    {% if "ROLE_GPT_MANAGER" in request.session.get("roles", []) %}
      <a href="/prompts" class="project-item">
        <div class="project-item-content">
          <div class="project-item-icon">💬</div>
          <h3 class="project-item-title">GPT Prompts</h3>
          <p>Manage prompts for GPT models.</p>
        </div>
      </a>
    {% else %}
      <div class="project-item disabled" onclick="showPermissionMessage()">
        <div class="project-item-content">
          <div class="project-item-icon">💬</div>
          <h3 class="project-item-title">GPT Prompts</h3>
          <p>Manage prompts for GPT models.</p>
        </div>
      </div>
    {% endif %}

    <!-- PYQs Admin Card -->
    {% if "ROLE_GPT_MANAGER" in request.session.get("roles", []) %}
      <a href="./pyqs_admin/exams" class="project-item">
        <div class="project-item-content">
          <div class="project-item-icon">📋</div>
          <h3 class="project-item-title">PYQs Admin</h3>
          <p>Manage exam details for previous year questions.</p>
        </div>
      </a>
    {% else %}
      <div class="project-item disabled" onclick="showPermissionMessage()">
        <div class="project-item-content">
          <div class="project-item-icon">📋</div>
          <h3 class="project-item-title">PYQs Admin</h3>
          <p>Manage exam details for previous year questions.</p>
        </div>
      </div>
    {% endif %}

    <!-- PDF Text Extractor Card -->
    {% if "ROLE_GPT_MANAGER" in request.session.get("roles", []) %}
      <a href="/pdf-text-extractor" class="project-item">
        <div class="project-item-content">
          <div class="project-item-icon">📄</div>
          <h3 class="project-item-title">PDF Text Extractor</h3>
          <p>Extract text from PDF resources.</p>
        </div>
      </a>
    {% else %}
      <div class="project-item disabled" onclick="showPermissionMessage()">
        <div class="project-item-content">
          <div class="project-item-icon">📄</div>
          <h3 class="project-item-title">PDF Text Extractor</h3>
          <p>Extract text from PDF resources.</p>
        </div>
      </div>
    {% endif %}
    <!-- Solution Creation Card -->
    {% if "ROLE_GPT_MANAGER" in request.session.get("roles", []) %}
      <a href="/solution-creation" class="project-item">
        <div class="project-item-content">
          <div class="project-item-icon">📝</div>
          <h3 class="project-item-title">Solution Creation</h3>
          <p>Create solutions for problems using text or images.</p>
        </div>
      </a>
    {% else %}
      <div class="project-item disabled" onclick="showPermissionMessage()">
        <div class="project-item-content">
          <div class="project-item-icon">📝</div>
          <h3 class="project-item-title">Solution Creation</h3>
          <p>Create solutions for problems using text or images.</p>
        </div>
      </div>
    {% endif %}


    <!-- TOC Extractor Card -->
    {% if "ROLE_GPT_MANAGER" in request.session.get("roles", []) %}
      <a href="/toc-extractor" class="project-item">
        <div class="project-item-content">
          <div class="project-item-icon">📑</div>
          <h3 class="project-item-title">TOC Extractor</h3>
          <p>Extract table of contents from PDF files.</p>
        </div>
      </a>
    {% else %}
      <div class="project-item disabled" onclick="showPermissionMessage()">
        <div class="project-item-content">
          <div class="project-item-icon">📑</div>
          <h3 class="project-item-title">TOC Extractor</h3>
          <p>Extract table of contents from PDF files.</p>
        </div>
      </div>
    {% endif %}

    <!-- MCQ Text Extraction Card -->
    {% if "ROLE_GPT_MANAGER" in request.session.get("roles", []) %}
      <a href="/mcq-text-extractor" class="project-item">
        <div class="project-item-content">
          <div class="project-item-icon">📝</div>
          <h3 class="project-item-title">MCQ Text Extraction</h3>
          <p>Extract text content from MCQ images.</p>
        </div>
      </a>
    {% else %}
      <div class="project-item disabled" onclick="showPermissionMessage()">
        <div class="project-item-content">
          <div class="project-item-icon">📝</div>
          <h3 class="project-item-title">MCQ Text Extraction</h3>
          <p>Extract text content from MCQ images.</p>
        </div>
      </div>
    {% endif %}

    <!-- MCQ Text Extraction Card -->
    {% if "ROLE_GPT_MANAGER" in request.session.get("roles", []) %}
      <a href="/mcq-translator" class="project-item">
        <div class="project-item-content">
          <div class="project-item-icon">📝</div>
          <h3 class="project-item-title">Translate PDF</h3>
          <p>Translate any PDF Content</p>
        </div>
      </a>
    {% else %}
      <div class="project-item disabled" onclick="showPermissionMessage()">
        <div class="project-item-content">
          <div class="project-item-icon">📝</div>
          <h3 class="project-item-title">Translate PDF</h3>
          <p>Translate any PDF Content</p>
        </div>
      </div>
    {% endif %}
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  function showPermissionMessage() {
    alert('Permission Denied: Please contact your administrator.');
  }
</script>
<style>
  /* Responsive grid adjustments */
  @media (max-width: 1024px) {
    .project-grid {
      grid-template-columns: repeat(2, 1fr) !important;
    }
  }

  @media (max-width: 768px) {
    .project-grid {
      grid-template-columns: 1fr !important;
    }
  }

  /* Adjust home container width for better layout */
  .home-container {
    max-width: 1200px !important;
  }
</style>
{% endblock %}