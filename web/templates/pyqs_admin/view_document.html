{% extends "base.html" %}

{% block title %}View Document | PYQs Admin | GPT Sir{% endblock %}

{% block head %}
<style>
  .pyqs-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .pyqs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }

  .back-btn {
    background-color: var(--light-gray);
    color: var(--gunmetal);
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
  }

  .back-btn:hover {
    background-color: #e0e0e0;
  }

  .document-info {
    background-color: var(--white);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .document-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--gunmetal);
  }

  .document-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 10px;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .meta-label {
    font-weight: 500;
    color: var(--gunmetal);
  }

  .meta-value {
    color: var(--dark-gray);
  }

  .pdf-container {
    background-color: var(--white);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: calc(100vh - 250px); /* Adjust based on header/footer height */
    min-height: 500px;
    position: relative;
    overflow: hidden;
  }

  #pdf-viewer {
    width: 100%;
    height: 100%;
    border: none;
    overflow-y: auto;
  }

  /* Custom scrollbar for the PDF viewer */
  #pdf-viewer::-webkit-scrollbar {
    width: 8px;
  }

  #pdf-viewer::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  #pdf-viewer::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }

  #pdf-viewer::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  /* Disable text selection in the PDF viewer */
  #pdf-viewer canvas {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  .loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--tea-green);
    animation: spin 1s ease-in-out infinite;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }
</style>
<!-- PDF.js library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.min.js"></script>
<script>
  // Set worker source
  pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.worker.min.js';
</script>
{% endblock %}

{% block content %}
<div class="pyqs-container">
  <div class="pyqs-header">
    <h1>View Document</h1>
    <a href="/pyqs_admin/exams/{{ exam.id }}/documents" class="back-btn">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
      </svg>
      Back to Documents
    </a>
  </div>

  <div class="document-info">
    <h2 class="document-title">
      {% if document.month and document.shift %}
        {{ exam.exam_name }} - {{ document.year }} {{ document.month }} ({{ document.shift }})
      {% elif document.month %}
        {{ exam.exam_name }} - {{ document.year }} {{ document.month }}
      {% elif document.shift %}
        {{ exam.exam_name }} - {{ document.year }} ({{ document.shift }})
      {% else %}
        {{ exam.exam_name }} - {{ document.year }}
      {% endif %}
    </h2>
    <div class="document-meta">
      <div class="meta-item">
        <span class="meta-label">Exam:</span>
        <span class="meta-value">{{ exam.exam_name }}</span>
      </div>
      <div class="meta-item">
        <span class="meta-label">Year:</span>
        <span class="meta-value">{{ document.year }}</span>
      </div>
      {% if document.month %}
        <div class="meta-item">
          <span class="meta-label">Month:</span>
          <span class="meta-value">{{ document.month }}</span>
        </div>
      {% endif %}
      {% if document.shift %}
        <div class="meta-item">
          <span class="meta-label">Shift:</span>
          <span class="meta-value">{{ document.shift }}</span>
        </div>
      {% endif %}
      <div class="meta-item">
        <span class="meta-label">Uploaded by:</span>
        <span class="meta-value">{{ document.created_by }}</span>
      </div>
      <div class="meta-item">
        <span class="meta-label">Upload date:</span>
        <span class="meta-value">{{ document.date_created.strftime('%d %b %Y') }}</span>
      </div>
    </div>
  </div>

  <div class="pdf-container">
    <div id="loading-indicator" class="loading-indicator">
      <div class="spinner"></div>
      <div>Loading PDF...</div>
    </div>
    <div id="pdf-viewer"></div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const pdfUrl = '/pyqs_admin/documents/{{ document.id }}/pdf';
    const container = document.getElementById('pdf-viewer');
    const loadingIndicator = document.getElementById('loading-indicator');

    // Create an iframe to our custom PDF viewer
    const iframe = document.createElement('iframe');
    iframe.src = `/static/pdfjs/viewer.html?file=${encodeURIComponent(pdfUrl)}`;
    iframe.style.width = '100%';
    iframe.style.height = '100%';
    iframe.style.border = 'none';

    // Hide loading indicator when iframe is loaded
    iframe.onload = function() {
      loadingIndicator.style.display = 'none';
    };

    // Add iframe to container
    container.appendChild(iframe);

    // Hide loading indicator after a timeout in case onload doesn't fire
    setTimeout(function() {
      loadingIndicator.style.display = 'none';
    }, 3000);
  });
</script>
{% endblock %}
