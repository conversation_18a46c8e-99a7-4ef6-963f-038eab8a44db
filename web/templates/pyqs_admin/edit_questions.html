{% extends "base.html" %}

{% block title %}Edit Questions | PYQs Admin | GPT Sir{% endblock %}

{% block head %}
<!-- KaTeX CSS and JavaScript -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>

<style>
  .pyqs-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .pyqs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }

  .back-btn {
    background-color: var(--light-gray);
    color: var(--gunmetal);
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
  }

  .back-btn:hover {
    background-color: #e0e0e0;
  }

  .document-info {
    background-color: var(--white);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .document-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--gunmetal);
  }

  .document-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 10px;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .meta-label {
    font-weight: 500;
    color: var(--gunmetal);
  }

  .meta-value {
    color: var(--dark-gray);
  }

  .questions-container {
    background-color: var(--white);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: calc(100vh - 250px); /* Adjust based on header/footer height */
    min-height: 500px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .questions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0;
  }

  .questions-list {
    flex: 1;
    overflow-y: auto;
    padding-right: 10px;
  }

  /* Custom scrollbar for the questions list */
  .questions-list::-webkit-scrollbar {
    width: 8px;
  }

  .questions-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  .questions-list::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }

  .questions-list::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  .questions-title {
    font-size: 1.2rem;
    color: var(--gunmetal);
    margin: 0;
  }

  .question-item {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
  }

  .question-header {
    background-color: #f8f9fa;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
  }

  .question-number {
    font-weight: 500;
    color: var(--gunmetal);
  }

  .question-type {
    font-size: 0.9rem;
    color: var(--dark-gray);
    padding: 4px 8px;
    background-color: #e9ecef;
    border-radius: 4px;
  }

  .question-content {
    padding: 20px;
    border-top: 1px solid #eee;
  }

  .form-group {
    margin-bottom: 15px;
  }

  .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--gunmetal);
  }

  .form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
  }

  textarea.form-control {
    min-height: 100px;
    resize: vertical;
  }

  .form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
  }

  .form-col {
    flex: 1;
  }

  .options-container {
    margin-bottom: 15px;
  }

  .option-group {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .option-label {
    width: 80px;
    font-weight: 500;
    color: var(--gunmetal);
  }

  .option-input {
    flex: 1;
  }

  .option-preview {
    margin-top: 5px;
    padding: 5px;
    background-color: #f9f9f9;
    border-radius: 4px;
    min-height: 24px;
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }

  .save-btn {
    background-color: var(--tea-green);
    color: var(--gunmetal);
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .save-btn:hover {
    background-color: #b5e0c0;
  }

  .delete-btn {
    background-color: #f8d7da;
    color: #721c24;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .delete-btn:hover {
    background-color: #f5c6cb;
  }

  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .pagination-item {
    margin: 0 5px;
  }

  .pagination-link {
    display: block;
    padding: 8px 12px;
    border-radius: 4px;
    text-decoration: none;
    background-color: var(--light-gray);
    color: var(--gunmetal);
    transition: background-color 0.3s;
  }

  .pagination-link:hover {
    background-color: #e0e0e0;
  }

  .pagination-link.active {
    background-color: var(--tea-green);
    color: var(--gunmetal);
  }

  .pagination-link.disabled {
    background-color: var(--light-gray);
    color: var(--dark-gray);
    cursor: not-allowed;
  }

  .no-questions {
    padding: 20px;
    text-align: center;
    color: var(--dark-gray);
  }

  .success-message {
    background-color: #d4edda;
    color: #155724;
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    display: none;
  }

  .error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    display: none;
  }
</style>
{% endblock %}

{% block content %}
<div class="pyqs-container">
  <div class="pyqs-header">
    <h1>Edit Questions</h1>
    <a href="/pyqs_admin/exams/{{ exam.id }}/documents" class="back-btn">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
      </svg>
      Back to Documents
    </a>
  </div>

  <div class="document-info">
    <h2 class="document-title">
      {% if document.month and document.shift %}
        {{ exam.exam_name }} - {{ document.year }} {{ document.month }} ({{ document.shift }})
      {% elif document.month %}
        {{ exam.exam_name }} - {{ document.year }} {{ document.month }}
      {% elif document.shift %}
        {{ exam.exam_name }} - {{ document.year }} ({{ document.shift }})
      {% else %}
        {{ exam.exam_name }} - {{ document.year }}
      {% endif %}
    </h2>
    <div class="document-meta">
      <div class="meta-item">
        <span class="meta-label">Exam:</span>
        <span class="meta-value">{{ exam.exam_name }}</span>
      </div>
      <div class="meta-item">
        <span class="meta-label">Year:</span>
        <span class="meta-value">{{ document.year }}</span>
      </div>
      {% if document.month %}
        <div class="meta-item">
          <span class="meta-label">Month:</span>
          <span class="meta-value">{{ document.month }}</span>
        </div>
      {% endif %}
      {% if document.shift %}
        <div class="meta-item">
          <span class="meta-label">Shift:</span>
          <span class="meta-value">{{ document.shift }}</span>
        </div>
      {% endif %}
    </div>
  </div>

  <div id="success-message" class="success-message"></div>
  <div id="error-message" class="error-message"></div>

  <div class="questions-container">
    <div class="questions-header">
      <h2 class="questions-title">Questions</h2>
    </div>

    <div class="questions-list">
      {% if solutions %}
        {% for solution in solutions %}
        <div class="question-item" id="question-{{ solution.id }}">
          <div class="question-header" onclick="toggleQuestion({{ solution.id }})">
            <div class="question-number">Question {{ solution.question_number or loop.index }}</div>
            <div class="question-type">{{ solution.question_type or 'Unknown Type' }}</div>
          </div>
          <div class="question-content" id="question-content-{{ solution.id }}" style="display: none;">
            <form id="question-form-{{ solution.id }}" onsubmit="saveQuestion(event, {{ solution.id }})">
              <div class="form-group">
                <label for="question_{{ solution.id }}">Question Text</label>
                <textarea id="question_{{ solution.id }}" name="question" class="form-control latex-editor" required>{{ solution.question }}</textarea>
                <div class="preview-container" id="preview-{{ solution.id }}" style="margin-top: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; background-color: #f9f9f9;">
                  <div class="preview-label" style="font-weight: 500; margin-bottom: 5px;">Preview:</div>
                  <div class="preview-content" id="preview-content-{{ solution.id }}"></div>
                </div>
              </div>
              <div class="form-row">
                <div class="form-col">
                  <div class="form-group">
                    <label for="question_type_{{ solution.id }}">Question Type</label>
                    <select id="question_type_{{ solution.id }}" name="question_type" class="form-control" onchange="toggleOptions({{ solution.id }})">
                      <option value="">Select Type</option>
                      <option value="MCQ" {% if solution.question_type == 'MCQ' %}selected{% endif %}>Multiple Choice</option>
                      <option value="Descriptive" {% if solution.question_type == 'Descriptive' %}selected{% endif %}>Descriptive</option>
                    </select>
                  </div>
                </div>
                <div class="form-col">
                  <div class="form-group">
                    <label for="marks_{{ solution.id }}">Marks</label>
                    <input type="number" id="marks_{{ solution.id }}" name="marks" class="form-control" step="0.01" value="{{ solution.marks or '' }}">
                  </div>
                </div>
                <div class="form-col">
                  <div class="form-group">
                    <label for="negative_mark_{{ solution.id }}">Negative Marks</label>
                    <input type="number" id="negative_mark_{{ solution.id }}" name="negative_mark" class="form-control" step="0.01" value="{{ solution.negative_mark or '' }}">
                  </div>
                </div>
              </div>
              <div id="options-container-{{ solution.id }}" class="options-container" style="display: {% if solution.question_type == 'MCQ' %}block{% else %}none{% endif %};">
                <div class="option-group">
                  <div class="option-label">Option 1:</div>
                  <div class="option-input">
                    <input type="text" id="option1_{{ solution.id }}" name="option1" class="form-control latex-input" value="{{ solution.option1 or '' }}">
                    <div class="option-preview" id="option1-preview-{{ solution.id }}"></div>
                  </div>
                </div>
                <div class="option-group">
                  <div class="option-label">Option 2:</div>
                  <div class="option-input">
                    <input type="text" id="option2_{{ solution.id }}" name="option2" class="form-control latex-input" value="{{ solution.option2 or '' }}">
                    <div class="option-preview" id="option2-preview-{{ solution.id }}"></div>
                  </div>
                </div>
                <div class="option-group">
                  <div class="option-label">Option 3:</div>
                  <div class="option-input">
                    <input type="text" id="option3_{{ solution.id }}" name="option3" class="form-control latex-input" value="{{ solution.option3 or '' }}">
                    <div class="option-preview" id="option3-preview-{{ solution.id }}"></div>
                  </div>
                </div>
                <div class="option-group">
                  <div class="option-label">Option 4:</div>
                  <div class="option-input">
                    <input type="text" id="option4_{{ solution.id }}" name="option4" class="form-control latex-input" value="{{ solution.option4 or '' }}">
                    <div class="option-preview" id="option4-preview-{{ solution.id }}"></div>
                  </div>
                </div>
                <div class="option-group">
                  <div class="option-label">Option 5:</div>
                  <div class="option-input">
                    <input type="text" id="option5_{{ solution.id }}" name="option5" class="form-control latex-input" value="{{ solution.option5 or '' }}">
                    <div class="option-preview" id="option5-preview-{{ solution.id }}"></div>
                  </div>
                </div>
                <div class="option-group">
                  <div class="option-label">Correct:</div>
                  <div class="option-input">
                    <input type="text" id="answer_{{ solution.id }}" name="answer" class="form-control" value="{{ solution.answer or '' }}" placeholder="Enter the correct option or answer">
                  </div>
                </div>
              </div>
              <div class="form-row">
                <div class="form-col">
                  <div class="form-group">
                    <label for="topic_{{ solution.id }}">Topic</label>
                    <input type="text" id="topic_{{ solution.id }}" name="topic" class="form-control" value="{{ solution.topic or '' }}">
                  </div>
                </div>
                <div class="form-col">
                  <div class="form-group">
                    <label for="subtopic_{{ solution.id }}">Subtopic</label>
                    <input type="text" id="subtopic_{{ solution.id }}" name="subtopic" class="form-control" value="{{ solution.subtopic or '' }}">
                  </div>
                </div>
              </div>

              <div class="action-buttons">
                <button type="button" class="delete-btn" onclick="deleteQuestion({{ solution.id }})">Delete</button>
                <button type="submit" class="save-btn">Save</button>
              </div>
            </form>
          </div>
        </div>
      {% endfor %}
    </div>

      {% if pages > 1 %}
        <div class="pagination">
          <div class="pagination-item">
            <a href="?page=1&limit={{ limit }}" class="pagination-link {% if page == 1 %}disabled{% endif %}">First</a>
          </div>
          <div class="pagination-item">
            <a href="?page={{ page - 1 }}&limit={{ limit }}" class="pagination-link {% if page == 1 %}disabled{% endif %}">Previous</a>
          </div>

          {% for p in range(max(1, page - 2), min(pages + 1, page + 3)) %}
            <div class="pagination-item">
              <a href="?page={{ p }}&limit={{ limit }}" class="pagination-link {% if p == page %}active{% endif %}">{{ p }}</a>
            </div>
          {% endfor %}

          <div class="pagination-item">
            <a href="?page={{ page + 1 }}&limit={{ limit }}" class="pagination-link {% if page == pages %}disabled{% endif %}">Next</a>
          </div>
          <div class="pagination-item">
            <a href="?page={{ pages }}&limit={{ limit }}" class="pagination-link {% if page == pages %}disabled{% endif %}">Last</a>
          </div>
        </div>
      {% endif %}
    {% else %}
      <div class="no-questions">
        No questions found. Please extract questions from the document first.
      </div>
    {% endif %}
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  // Toggle question content visibility
  function toggleQuestion(id) {
    const content = document.getElementById(`question-content-${id}`);
    if (content.style.display === 'none') {
      content.style.display = 'block';

      // Render LaTeX in the question
      renderMathInElement(content, {
        delimiters: [
          { left: "\\(", right: "\\)", display: false },
          { left: "\\[", right: "\\]", display: true },
          { left: "$$", right: "$$", display: true },
          { left: "$", right: "$", display: false },
        ],
        ignoredTags: ['textarea', 'input']
      });

      // Set up live preview for LaTeX
      setupLatexPreview(id);
    } else {
      content.style.display = 'none';
    }
  }

  // Set up live preview for LaTeX inputs
  function setupLatexPreview(id) {
    // Question preview
    const questionTextarea = document.getElementById(`question_${id}`);
    const previewContent = document.getElementById(`preview-content-${id}`);

    if (questionTextarea && previewContent) {
      // Initial render
      previewContent.innerHTML = questionTextarea.value;
      renderMathInElement(previewContent, {
        delimiters: [
          { left: "\\(", right: "\\)", display: false },
          { left: "\\[", right: "\\]", display: true },
          { left: "$$", right: "$$", display: true },
          { left: "$", right: "$", display: false },
        ],
        throwOnError: false
      });

      // Live update
      questionTextarea.addEventListener('input', function() {
        previewContent.innerHTML = this.value;
        renderMathInElement(previewContent, {
          delimiters: [
            { left: "\\(", right: "\\)", display: false },
            { left: "\\[", right: "\\]", display: true },
            { left: "$$", right: "$$", display: true },
            { left: "$", right: "$", display: false },
          ],
          throwOnError: false
        });
      });
    }

    // Options preview
    for (let i = 1; i <= 5; i++) {
      const optionInput = document.getElementById(`option${i}_${id}`);
      const optionPreview = document.getElementById(`option${i}-preview-${id}`);

      if (optionInput && optionPreview) {
        // Initial render
        optionPreview.innerHTML = optionInput.value;
        renderMathInElement(optionPreview, {
          delimiters: [
            { left: "\\(", right: "\\)", display: false },
            { left: "\\[", right: "\\]", display: true },
            { left: "$$", right: "$$", display: true },
            { left: "$", right: "$", display: false },
          ],
          throwOnError: false
        });

        // Live update
        optionInput.addEventListener('input', function() {
          optionPreview.innerHTML = this.value;
          renderMathInElement(optionPreview, {
            delimiters: [
              { left: "\\(", right: "\\)", display: false },
              { left: "\\[", right: "\\]", display: true },
              { left: "$$", right: "$$", display: true },
              { left: "$", right: "$", display: false },
            ],
            throwOnError: false
          });
        });
      }
    }
  }

  // Toggle options visibility based on question type
  function toggleOptions(id) {
    const questionType = document.getElementById(`question_type_${id}`).value;
    const optionsContainer = document.getElementById(`options-container-${id}`);

    if (questionType === 'MCQ') {
      optionsContainer.style.display = 'block';
    } else {
      optionsContainer.style.display = 'none';
    }
  }

  // Save question
  function saveQuestion(event, id) {
    event.preventDefault();

    const form = document.getElementById(`question-form-${id}`);
    const formData = new FormData(form);

    // Convert FormData to JSON
    const data = {};
    formData.forEach((value, key) => {
      data[key] = value;
    });

    // Send AJAX request
    fetch(`/pyqs_admin/solutions/${id}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showSuccessMessage(data.message || 'Question saved successfully');
      } else {
        showErrorMessage(data.error || 'Failed to save question');
      }
    })
    .catch(error => {
      showErrorMessage(`Error: ${error.message}`);
    });
  }

  // Delete question
  function deleteQuestion(id) {
    if (confirm('Are you sure you want to delete this question?')) {
      fetch(`/pyqs_admin/solutions/${id}`, {
        method: 'DELETE'
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Remove the question from the DOM
          const questionItem = document.getElementById(`question-${id}`);
          questionItem.remove();

          showSuccessMessage(data.message || 'Question deleted successfully');
        } else {
          showErrorMessage(data.error || 'Failed to delete question');
        }
      })
      .catch(error => {
        showErrorMessage(`Error: ${error.message}`);
      });
    }
  }

  // Show success message
  function showSuccessMessage(message) {
    const successMessage = document.getElementById('success-message');
    successMessage.textContent = message;
    successMessage.style.display = 'block';

    // Hide after 3 seconds
    setTimeout(() => {
      successMessage.style.display = 'none';
    }, 3000);
  }

  // Show error message
  function showErrorMessage(message) {
    const errorMessage = document.getElementById('error-message');
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';

    // Hide after 5 seconds
    setTimeout(() => {
      errorMessage.style.display = 'none';
    }, 5000);
  }

  // Initialize KaTeX rendering
  document.addEventListener('DOMContentLoaded', function() {
    // Render LaTeX in visible elements
    const visibleElements = document.querySelectorAll('.question-content[style="display: block"]');
    visibleElements.forEach(element => {
      renderMathInElement(element, {
        delimiters: [
          { left: "\\(", right: "\\)", display: false },
          { left: "\\[", right: "\\]", display: true },
          { left: "$$", right: "$$", display: true },
          { left: "$", right: "$", display: false },
        ],
        ignoredTags: ['textarea', 'input']
      });

      // Set up LaTeX preview for visible questions
      const id = element.id.replace('question-content-', '');
      setupLatexPreview(id);
    });

    // Add helper text for LaTeX
    const helperText = document.createElement('div');
    helperText.className = 'latex-helper';
    helperText.style.padding = '10px';
    helperText.style.marginBottom = '20px';
    helperText.style.backgroundColor = '#f8f9fa';
    helperText.style.borderRadius = '4px';
    helperText.style.fontSize = '0.9rem';
    helperText.innerHTML = `
      <p><strong>LaTeX Tips:</strong> You can use LaTeX for mathematical expressions.</p>
      <ul>
        <li>Inline math: $x^2 + y^2 = z^2$</li>
        <li>Display math: $$E = mc^2$$</li>
        <li>Fractions: $\\frac{a}{b}$</li>
        <li>Square roots: $\\sqrt{x}$</li>
        <li>Subscripts: $x_i$</li>
        <li>Superscripts: $x^n$</li>
      </ul>
    `;

    // Insert helper text at the top of the questions list
    const questionsList = document.querySelector('.questions-list');
    if (questionsList) {
      questionsList.insertBefore(helperText, questionsList.firstChild);

      // Render LaTeX in the helper text
      renderMathInElement(helperText, {
        delimiters: [
          { left: "\\(", right: "\\)", display: false },
          { left: "\\[", right: "\\]", display: true },
          { left: "$$", right: "$$", display: true },
          { left: "$", right: "$", display: false },
        ],
        throwOnError: false
      });
    }
  });
</script>
{% endblock %}
