{% extends "base.html" %}

{% block title %}{{ exam.exam_name }} Documents | PYQs Admin | GPT Sir{% endblock %}

{% block head %}
<style>
  .pyqs-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .pyqs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }

  .back-btn {
    background-color: var(--light-gray);
    color: var(--gunmetal);
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
  }

  .back-btn:hover {
    background-color: #e0e0e0;
  }

  .upload-form {
    background-color: var(--white);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .form-title {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: var(--gunmetal);
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
  }

  .form-group {
    margin-bottom: 15px;
  }

  .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--gunmetal);
  }

  .form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
  }

  .file-input-group {
    margin-bottom: 20px;
  }

  .file-input-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--gunmetal);
  }

  .file-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: var(--white);
  }

  .submit-btn {
    background-color: var(--tea-green);
    color: var(--gunmetal);
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .submit-btn:hover {
    background-color: #b5e0c0;
  }

  .documents-list {
    background-color: var(--white);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .documents-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .documents-title {
    font-size: 1.2rem;
    color: var(--gunmetal);
    margin: 0;
  }

  .document-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
  }

  .document-item:last-child {
    border-bottom: none;
  }

  .document-info {
    flex: 1;
  }

  .document-name {
    font-weight: 500;
    color: var(--gunmetal);
    margin-bottom: 5px;
  }

  .document-meta {
    color: var(--dark-gray);
    font-size: 0.9rem;
  }

  .document-actions {
    display: flex;
    gap: 10px;
  }

  .action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 0.9rem;
    text-decoration: none;
    transition: background-color 0.3s;
  }

  .view-btn {
    background-color: var(--light-blue);
    color: var(--white);
  }

  .view-btn:hover {
    background-color: #0056b3;
  }

  .extract-btn {
    background-color: var(--tea-green);
    color: var(--gunmetal);
  }

  .extract-btn:hover {
    background-color: #b5e0c0;
  }

  .edit-btn {
    background-color: var(--light-gray);
    color: var(--gunmetal);
  }

  .edit-btn:hover {
    background-color: #e0e0e0;
  }

  .delete-btn {
    background-color: #dc3545;
    color: white;
  }

  .delete-btn:hover {
    background-color: #c82333;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .pagination-item {
    margin: 0 5px;
  }

  .pagination-link {
    display: block;
    padding: 8px 12px;
    border-radius: 4px;
    text-decoration: none;
    background-color: var(--light-gray);
    color: var(--gunmetal);
    transition: background-color 0.3s;
  }

  .pagination-link:hover {
    background-color: #e0e0e0;
  }

  .pagination-link.active {
    background-color: var(--tea-green);
    color: var(--gunmetal);
  }

  .pagination-link.disabled {
    background-color: var(--light-gray);
    color: var(--dark-gray);
    cursor: not-allowed;
  }

  .error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
  }

  .no-documents {
    padding: 20px;
    text-align: center;
    color: var(--dark-gray);
  }

  /* Animation for the extraction modal spinner */
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>
{% endblock %}

{% block content %}
<div class="pyqs-container">
  <div class="pyqs-header">
    <h1>{{ exam.exam_name }} Documents</h1>
    <a href="/pyqs_admin/exams" class="back-btn">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
      </svg>
      Back to Exams
    </a>
  </div>

  {% if error %}
    <div class="error-message">
      {{ error }}
    </div>
  {% endif %}

  <div class="upload-form">
    <h2 class="form-title">Upload Question Paper</h2>
    <form action="/pyqs_admin/exams/{{ exam.id }}/documents" method="post" enctype="multipart/form-data">
      <div class="form-grid">
        <div class="form-group">
          <label for="year">Year (required)</label>
          <input type="number" id="year" name="year" class="form-control" required min="1900" max="2100" value="{{ current_year }}">
        </div>
        <div class="form-group">
          <label for="month">Month (optional)</label>
          <select id="month" name="month" class="form-control">
            <option value="">Select Month</option>
            <option value="January">January</option>
            <option value="February">February</option>
            <option value="March">March</option>
            <option value="April">April</option>
            <option value="May">May</option>
            <option value="June">June</option>
            <option value="July">July</option>
            <option value="August">August</option>
            <option value="September">September</option>
            <option value="October">October</option>
            <option value="November">November</option>
            <option value="December">December</option>
          </select>
        </div>
        <div class="form-group">
          <label for="shift">Shift (optional)</label>
          <input type="text" id="shift" name="shift" class="form-control" placeholder="e.g., Morning, Evening, Shift 1">
        </div>
      </div>
      <div class="file-input-group">
        <label for="file" class="file-input-label">PDF File (required)</label>
        <input type="file" id="file" name="file" class="file-input" accept=".pdf" required>
      </div>
      <button type="submit" class="submit-btn">Upload Document</button>
    </form>
  </div>

  <div class="documents-list">
    <div class="documents-header">
      <h2 class="documents-title">Uploaded Documents</h2>
    </div>

    {% if documents %}
      {% for document in documents %}
        <div class="document-item">
          <div class="document-info">
            <div class="document-name">
              {% if document.month and document.shift %}
                {{ document.year }} {{ document.month }} ({{ document.shift }})
              {% elif document.month %}
                {{ document.year }} {{ document.month }}
              {% elif document.shift %}
                {{ document.year }} ({{ document.shift }})
              {% else %}
                {{ document.year }}
              {% endif %}
            </div>
            <div class="document-meta">
              Uploaded on {{ document.date_created.strftime('%d %b %Y') }}
            </div>
          </div>
          <div class="document-actions">
            <a href="/pyqs_admin/documents/{{ document.id }}/view" class="action-btn view-btn">View PDF</a>

            {% if not document.has_extracted_content %}
              <button class="action-btn extract-btn" onclick="extractContent({{ document.id }})">Extract HTML</button>
            {% else %}
              <a href="/pyqs_admin/documents/{{ document.id }}/view-content" class="action-btn view-btn">View HTML</a>
            {% endif %}

            {% if not document.has_solutions %}
              <button class="action-btn extract-btn" onclick="extractQuestions({{ document.id }})">Extract Questions</button>
            {% else %}
              <a href="/pyqs_admin/documents/{{ document.id }}/edit-questions" class="action-btn edit-btn">Edit Questions</a>
            {% endif %}

            <button class="action-btn delete-btn" onclick="deleteDocument({{ document.id }})">Delete Document</button>
          </div>
        </div>
      {% endfor %}

      {% if pages > 1 %}
        <div class="pagination">
          <div class="pagination-item">
            <a href="?page=1&limit={{ limit }}" class="pagination-link {% if page == 1 %}disabled{% endif %}">First</a>
          </div>
          <div class="pagination-item">
            <a href="?page={{ page - 1 }}&limit={{ limit }}" class="pagination-link {% if page == 1 %}disabled{% endif %}">Previous</a>
          </div>

          {% for p in range(max(1, page - 2), min(pages + 1, page + 3)) %}
            <div class="pagination-item">
              <a href="?page={{ p }}&limit={{ limit }}" class="pagination-link {% if p == page %}active{% endif %}">{{ p }}</a>
            </div>
          {% endfor %}

          <div class="pagination-item">
            <a href="?page={{ page + 1 }}&limit={{ limit }}" class="pagination-link {% if page == pages %}disabled{% endif %}">Next</a>
          </div>
          <div class="pagination-item">
            <a href="?page={{ pages }}&limit={{ limit }}" class="pagination-link {% if page == pages %}disabled{% endif %}">Last</a>
          </div>
        </div>
      {% endif %}
    {% else %}
      <div class="no-documents">
        No documents uploaded yet.
      </div>
    {% endif %}
  </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Add modal for extraction progress -->
<div id="extraction-modal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.4);">
  <div class="modal-content" style="background-color: #fefefe; margin: 15% auto; padding: 20px; border: 1px solid #888; width: 80%; max-width: 400px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); text-align: center;">
    <h2 id="modal-title" style="margin-top: 0;">Extracting Content</h2>
    <div id="loader-container" style="margin: 30px 0;">
      <div class="spinner" style="display: inline-block; width: 50px; height: 50px; border: 5px solid rgba(0,0,0,0.1); border-radius: 50%; border-top-color: var(--tea-green); animation: spin 1s linear infinite;"></div>
      <div id="elapsed-time" style="margin-top: 20px; font-size: 18px; font-weight: 500;">Time: 00:00:00</div>
      <div id="progress-text" style="margin-top: 15px;">Processing...</div>
    </div>
    <div id="modal-message" style="margin-top: 20px; color: #721c24; background-color: #f8d7da; padding: 10px; border-radius: 4px; display: none;"></div>
  </div>
</div>

<script>
  // Track start time for elapsed time calculation
  let startTime;
  let timerInterval;

  // Function to show the extraction modal
  function showExtractionModal(title) {
    document.getElementById('modal-title').textContent = title;
    document.getElementById('elapsed-time').textContent = 'Time: 00:00:00';
    document.getElementById('progress-text').textContent = 'Processing...';
    document.getElementById('modal-message').style.display = 'none';

    // Record start time
    startTime = new Date();

    // Start timer for elapsed time
    if (timerInterval) clearInterval(timerInterval);
    timerInterval = setInterval(updateElapsedTime, 1000);

    document.getElementById('extraction-modal').style.display = 'block';

    // Set a timeout to hide the modal after 5 minutes in case the complete event is never fired
    setTimeout(() => {
      hideExtractionModal();
    }, 5 * 60 * 1000);
  }

  // Function to hide the extraction modal
  function hideExtractionModal() {
    document.getElementById('extraction-modal').style.display = 'none';

    // Clear the timer interval
    if (timerInterval) {
      clearInterval(timerInterval);
      timerInterval = null;
    }

    // Log for debugging
    console.log('Modal hidden');
  }

  // Function to update elapsed time
  function updateElapsedTime() {
    const now = new Date();
    const elapsedSeconds = Math.floor((now - startTime) / 1000);

    // Format time as HH:MM:SS
    const hours = Math.floor(elapsedSeconds / 3600);
    const minutes = Math.floor((elapsedSeconds % 3600) / 60);
    const seconds = elapsedSeconds % 60;

    const formattedTime = [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      seconds.toString().padStart(2, '0')
    ].join(':');

    document.getElementById('elapsed-time').textContent = `Time: ${formattedTime}`;
  }

  // Function to update progress
  function updateProgress(percent, message) {
    // Update message if provided
    if (message) {
      document.getElementById('progress-text').textContent = message;
    }

    // Log for debugging
    console.log(`Progress update: ${percent}%, message: ${message || 'none'}`);
  }

  // Function to show error message
  function showModalError(message) {
    const modalMessage = document.getElementById('modal-message');
    modalMessage.textContent = message;
    modalMessage.style.display = 'block';
  }

  // Function to extract content with progress tracking
  function extractContent(documentId) {
    if (confirm('Are you sure you want to extract HTML content from this document?')) {
      // Show the extraction modal
      showExtractionModal('Extracting HTML Content');

      // Start the extraction process
      fetch(`/pyqs_admin/documents/${documentId}/extract-content`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      .then(response => {
        // Check if the response is ok (status in the range 200-299)
        if (!response.ok) {
          // If not, throw an error with the status
          return response.json().then(data => {
            throw new Error(data.error || `HTTP error! Status: ${response.status}`);
          });
        }
        // Process the response
        return response.json();
      })
      .then(data => {
        console.log('Extraction response:', data);

        // Hide the modal
        hideExtractionModal();

        if (data.success) {
          // Show success message
          alert('HTML content extracted successfully!');

          // Reload the page
          location.reload();
        } else {
          // Show error message
          showModalError(data.error || 'Unknown error');
        }
      })
      .catch(error => {
        console.error('Extraction error:', error);
        hideExtractionModal();

        // Show a more user-friendly error message
        const errorMessage = error.message || 'An unknown error occurred';
        alert(`Error extracting HTML content: ${errorMessage}`);
      });
    }
  }

  function extractQuestions(documentId) {
    if (confirm('Are you sure you want to extract questions from this document? This will replace any existing questions.')) {
      // Show the extraction modal
      showExtractionModal('Extracting Questions');

      // Start the extraction process
      fetch(`/pyqs_admin/documents/${documentId}/extract-questions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      .then(response => {
        // Check if the response is ok (status in the range 200-299)
        if (!response.ok) {
          // If not, throw an error with the status
          return response.json().then(data => {
            throw new Error(data.error || `HTTP error! Status: ${response.status}`);
          });
        }
        // Process the response
        return response.json();
      })
      .then(data => {
        console.log('Question extraction response:', data);

        // Hide the modal
        hideExtractionModal();

        if (data.success) {
          // Show success message
          alert('Questions extracted successfully!');

          // Reload the page
          location.reload();
        } else {
          // Show error message
          showModalError(data.error || 'Unknown error');
        }
      })
      .catch(error => {
        console.error('Question extraction error:', error);
        hideExtractionModal();

        // Show a more user-friendly error message
        const errorMessage = error.message || 'An unknown error occurred';
        alert(`Error extracting questions: ${errorMessage}`);
      });
    }
  }

  function deleteDocument(documentId) {
    if (confirm('Are you sure you want to delete this document? This will permanently delete the document, extracted content, and all questions.')) {
      fetch(`/pyqs_admin/documents/${documentId}/delete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          alert('Document deleted successfully!');
          location.reload();
        } else {
          alert(`Error: ${data.error || 'Unknown error'}`);
        }
      })
      .catch(error => {
        alert(`Error: ${error.message}`);
      });
    }
  }

  // Set current year as default
  document.addEventListener('DOMContentLoaded', function() {
    const yearInput = document.getElementById('year');
    if (yearInput && !yearInput.value) {
      yearInput.value = new Date().getFullYear();
    }
  });
</script>
{% endblock %}
