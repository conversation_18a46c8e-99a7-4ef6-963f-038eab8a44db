{% extends "base.html" %}

{% block title %}View Extracted Content | PYQs Admin | GPT Sir{% endblock %}

{% block head %}
<!-- KaTeX CSS and JavaScript -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>

<style>
  :root {
    --white: #ffffff;
    --light-gray: #f5f5f5;
    --dark-gray: #666666;
    --gunmetal: #2c3e50;
    --tea-green: #d0f0c0;
  }

  .pyqs-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .pyqs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap; /* Ensure elements wrap if they exceed the container width */
    gap: 10px; /* Add spacing between wrapped elements */
  }

  .back-btn {
    background-color: var(--light-gray);
    color: var(--gunmetal);
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
    max-width: fit-content; /* Ensure the button does not stretch unnecessarily */
    box-sizing: border-box; /* Include padding in width calculations */
  }

  .back-btn:hover {
    background-color: #e0e0e0;
  }

  .document-info {
    background-color: var(--white);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .document-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--gunmetal);
  }

  .document-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 10px;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .meta-label {
    font-weight: 500;
    color: var(--gunmetal);
  }

  .meta-value {
    color: var(--dark-gray);
  }

  .content-container {
    background-color: var(--white);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: calc(100vh - 250px); /* Adjust based on header/footer height */
    min-height: 500px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .content-header {
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;
  }

  .content-body {
    flex: 1;
    overflow: auto;
    padding-right: 10px;
    background-color: #ffffff; /* Set white background for the viewer */
  }

  /* Custom scrollbar for the content */
  .content-body::-webkit-scrollbar {
    width: 8px;
  }

  .content-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  .content-body::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }

  .content-body::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  .page-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
  }

  .page-btn {
    padding: 5px 10px;
    background-color: var(--tea-green);
    color: var(--gunmetal);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
  }

  .page-btn:hover {
    background-color: #b5e0c0;
  }

  .page-btn:disabled {
    background-color: #f1f1f1;
    color: #999;
    cursor: not-allowed;
  }

  #page-info {
    font-weight: 500;
    color: var(--gunmetal);
  }

  .extracted-content {
  font-family: 'Arial', sans-serif;
  line-height: 1.6;
  color: var(--gunmetal);
  max-width: 100%;
  word-break: break-word;
  white-space: pre-wrap;
  overflow-x: auto;
}


  /* Styling for the extracted content */
  .extracted-content h1,
  .extracted-content h2,
  .extracted-content h3,
  .extracted-content h4,
  .extracted-content h5,
  .extracted-content h6 {
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    color: var(--gunmetal);
  }

  .extracted-content h1 {
    font-size: 1.8rem;
  }

  .extracted-content h2 {
    font-size: 1.6rem;
  }

  .extracted-content h3 {
    font-size: 1.4rem;
  }

  .extracted-content p {
    margin-bottom: 1em;
  }

  .extracted-content ul,
  .extracted-content ol {
    margin-bottom: 1em;
    padding-left: 2em;
  }

  .extracted-content li {
    margin-bottom: 0.5em;
  }

  .extracted-content img {
    max-width: 100%;
    height: auto;
    margin: 1em 0;
  }

  .extracted-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
  }

  .extracted-content th,
  .extracted-content td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
  }

  .extracted-content th {
    background-color: #f2f2f2;
  }

  .extracted-content .table-wrapper {
    overflow-x: auto;
    margin-bottom: 1em;
  }

  .extracted-content .pdf-page {
    margin-bottom: 2em;
    padding-bottom: 2em;
    border-bottom: 1px dashed #ccc;
  }

  .extracted-content .pdf-page:last-child {
    border-bottom: none;
  }
</style>
{% endblock %}

{% block content %}
<div class="pyqs-container">
  <div class="pyqs-header">
    <h1>View Extracted Content</h1>
    <a href="/pyqs_admin/exams/{{ exam.id }}/documents" class="back-btn">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
      </svg>
      Back to Documents
    </a>
  </div>

  <div class="document-info">
    <h2 class="document-title">
      {% if document.month and document.shift %}
        {{ exam.exam_name }} - {{ document.year }} {{ document.month }} ({{ document.shift }})
      {% elif document.month %}
        {{ exam.exam_name }} - {{ document.year }} {{ document.month }}
      {% elif document.shift %}
        {{ exam.exam_name }} - {{ document.year }} ({{ document.shift }})
      {% else %}
        {{ exam.exam_name }} - {{ document.year }}
      {% endif %}
    </h2>
    <div class="document-meta">
      <div class="meta-item">
        <span class="meta-label">Exam:</span>
        <span class="meta-value">{{ exam.exam_name }}</span>
      </div>
      <div class="meta-item">
        <span class="meta-label">Year:</span>
        <span class="meta-value">{{ document.year }}</span>
      </div>
      {% if document.month %}
        <div class="meta-item">
          <span class="meta-label">Month:</span>
          <span class="meta-value">{{ document.month }}</span>
        </div>
      {% endif %}
      {% if document.shift %}
        <div class="meta-item">
          <span class="meta-label">Shift:</span>
          <span class="meta-value">{{ document.shift }}</span>
        </div>
      {% endif %}
      <div class="meta-item">
        <span class="meta-label">Uploaded by:</span>
        <span class="meta-value">{{ document.created_by }}</span>
      </div>
      <div class="meta-item">
        <span class="meta-label">Upload date:</span>
        <span class="meta-value">{{ document.date_created.strftime('%d %b %Y') }}</span>
      </div>
    </div>
  </div>

  <div class="content-container">
    <div class="content-body">
      <div class="extracted-content">
        {{ document.extracted_content | safe }}
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Make all PDF pages visible
    const pdfPages = document.querySelectorAll('.pdf-page');
    pdfPages.forEach(page => {
      page.style.display = 'block';
    });

    // Render LaTeX formulas
    renderMathInElement(document.querySelector('.extracted-content'), {
      delimiters: [
        { left: "\\(", right: "\\)", display: false },
        { left: "\\[", right: "\\]", display: true },
        { left: "$$", right: "$$", display: true },
        { left: "$", right: "$", display: false },
      ],
      ignoredTags: [],
      trust: true,
      strict: false,
      throwOnError: false
    });
  });
</script>
{% endblock %}
