{% extends "base.html" %}

{% block title %}Error | GPT Sir{% endblock %}

{% block content %}
<main class="content-container">
  <div class="error-wrapper">
    <h1>Error</h1>
    
    <div class="error-message-container">
      <div class="error-icon">❌</div>
      <div class="error-text">{{ error }}</div>
    </div>
    
    <div class="action-buttons">
      <a href="/" class="action-button">Return to Home</a>
    </div>
  </div>
</main>
{% endblock %}

{% block styles %}
<style>
  .error-wrapper {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
  }
  
  .error-message-container {
    background-color: #fdecea;
    border-radius: 10px;
    padding: 2rem;
    margin: 2rem 0;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .error-icon {
    font-size: 3rem;
    color: #d32f2f;
    margin-bottom: 1rem;
  }
  
  .error-text {
    color: #d32f2f;
    font-size: 1.2rem;
    line-height: 1.6;
  }
  
  .action-buttons {
    margin-top: 2rem;
  }
  
  .action-button {
    background-color: var(--cerise);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  
  .action-button:hover {
    background-color: var(--bittersweet);
  }
</style>
{% endblock %}
