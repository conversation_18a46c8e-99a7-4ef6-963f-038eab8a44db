{% extends "base.html" %}

{% block title %}Question Paper Extractor | GPT Sir{% endblock %}

{% block styles %}
<style>
  /* Page results styles */
  .page-results {
    margin-top: 1.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 1rem;
    background-color: #f9f9f9;
  }

  .page-content-box {
    max-height: 500px;
    overflow-y: auto;
    padding: 0.5rem;
  }

  /* Styles for action buttons */
  .action-buttons {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
  }

  .action-button {
    background-color: var(--cerise);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .action-button:hover {
    background-color: var(--bittersweet);
  }

  /* Styles for cost and time info */
  .info-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
  }

  .cost-info, .time-info {
    font-size: 0.9rem;
    color: var(--gunmetal);
  }

  #costValue, #timeValue {
    font-weight: bold;
    color: var(--cerise);
  }

  /* Styles for combined results */
  .combined-results {
    margin-top: 1.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 1.5rem;
    background-color: white;
    max-height: 600px;
    overflow-y: auto;
  }

  .content-section {
    margin-bottom: 1rem;
  }

  .page-divider {
    margin: 1.5rem 0;
    border: 0;
    border-top: 1px dashed #ccc;
  }
</style>
{% endblock %}

{% block content %}
<main class="upload-wrapper">
  <h2>Question Paper Extractor</h2>

  <form id="upload-form" action="/upload" method="post" enctype="multipart/form-data" {% if message %}style="display: none;"{% endif %}>
    <input type="file" name="file" accept=".pdf" required>
    <button type="submit">Upload</button>
  </form>

  <div id="loadingIndicator" class="loading-indicator" style="display: none;">
    <div class="spinner"></div>
    <p>Processing your file... This may take a few minutes.</p>
    <div id="progressContainer" class="progress-container">
      <div id="progressBar" class="progress-bar"></div>
      <div id="progressText" class="progress-text">0%</div>
    </div>
    <div id="timer" class="timer-display"></div>
  </div>

  {% if message %}
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
      <h3>Extraction Results</h3>
      <div class="info-container">
        <div class="cost-info">
          <span>Cost: </span>
          <span id="costValue">₹{{ cost|default('0.00') }}</span>
        </div>
        {% if processing_time %}
        <div class="time-info">
          <span>Time: </span>
          <span id="timeValue">{{ processing_time }}</span>
        </div>
        {% endif %}
      </div>
    </div>

  <div class="result-content">
    <p style="color: green; margin-bottom: 1rem;">{{ message }}</p>

    <!-- Display combined validation results (no page tabs) -->
    {% if validation_results and validation_results.page_results %}
      <div class="combined-results">
        {% for i in range(1, page_count + 1) %}
          {% set page_key = 'page_' ~ i %}
          {% if page_key in validation_results.page_results %}
            {% set page_result = validation_results.page_results[page_key] %}
            <div class="content-section">
              {{ page_result.corrected_text|default(page_result.extracted_text)|safe }}
            </div>
            {% if i < page_count %}<hr class="page-divider">{% endif %}
          {% endif %}
        {% endfor %}
      </div>
    {% else %}
      {% if 'Output saved to:' in message %}
        {% set filename = message.split('Output saved to: ')[1] %}
        <p>
          <a href="/preview/{{ filename }}" target="_blank" class="view-result-btn">➡️ View Extracted HTML</a>
        </p>
      {% endif %}
    {% endif %}

    <!-- No Extract Another PDF button as requested -->
  </div>
  {% else %}
    <div id="resultContainer" style="display: none; margin-bottom: 1.5rem;">
      <div style="display: flex; justify-content: space-between; align-items: center;">
        <h3>Extraction Results</h3>
        <div class="cost-info">
          <span>Total LLM Cost: </span>
          <span id="costValue">₹0.00</span>
        </div>
      </div>
    </div>
  {% endif %}
</main>
{% endblock %}

{% block scripts %}
<script>

  document.addEventListener('DOMContentLoaded', function() {
    {% if not message %}
      const form = document.getElementById("upload-form");
      const loadingIndicator = document.getElementById("loadingIndicator");
      const resultContainer = document.getElementById("resultContainer");
      const timerDisplay = document.getElementById("timer");
      const progressBar = document.getElementById("progressBar");
      const progressText = document.getElementById("progressText");
      const costValue = document.getElementById("costValue");

      function formatDuration(seconds) {
        const hrs = Math.floor(seconds / 3600);
        const mins = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        let parts = [];
        if (hrs) parts.push(`${hrs} hr`);
        if (mins) parts.push(`${mins} min`);
        if (secs || (!hrs && !mins)) parts.push(`${secs} sec`);

        return parts.join(" ");
      }

      function simulateProgress() {
        let progress = 0;
        const maxProgress = 95; // Only go to 95% with simulation

        return setInterval(() => {
          // Simulate non-linear progress (faster at start, slower at end)
          const increment = Math.max(0.5, (100 - progress) / 50);
          progress = Math.min(maxProgress, progress + increment);

          progressBar.style.width = `${progress}%`;
          progressText.textContent = `${Math.round(progress)}%`;
        }, 1000);
      }

      form.addEventListener("submit", function () {
        loadingIndicator.style.display = "flex";
        resultContainer.style.display = "none";
        timerDisplay.textContent = "";

        // Reset progress bar
        progressBar.style.width = "0%";
        progressText.textContent = "0%";

        // Start progress simulation
        window.progressInterval = simulateProgress();

        // Start timer
        window.startTime = new Date();
        window.timerInterval = setInterval(() => {
          const now = new Date();
          const elapsedSeconds = (now - window.startTime) / 1000;
          const humanTime = formatDuration(elapsedSeconds);
          timerDisplay.textContent = `⏱️ Processing Time: ${humanTime}`;
        }, 500);
      });
    {% endif %}
  });
</script>
{% endblock %}