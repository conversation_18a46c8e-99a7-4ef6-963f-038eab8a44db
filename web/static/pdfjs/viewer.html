<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>PDF Viewer</title>
  <style>
    body, html {
      margin: 0;
      padding: 0;
      height: 100%;
      overflow: hidden;
    }
    #pdf-container {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    #pdf-viewer {
      flex: 1;
      border: none;
      width: 100%;
      height: 100%;
    }
  </style>
</head>
<body>
  <div id="pdf-container">
    <iframe id="pdf-viewer" src="about:blank"></iframe>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Get the PDF URL from the query parameter
      const urlParams = new URLSearchParams(window.location.search);
      const pdfUrl = urlParams.get('file');
      
      if (pdfUrl) {
        // Set the iframe source to the PDF URL
        document.getElementById('pdf-viewer').src = pdfUrl;
      } else {
        document.body.innerHTML = '<p>Error: No PDF URL specified</p>';
      }
    });
  </script>
</body>
</html>
