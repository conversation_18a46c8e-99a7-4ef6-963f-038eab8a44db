"""
Utilities for storing and retrieving extraction results.
"""

import os
import json
import uuid
import time
import logging
from typing import Dict, Any, Optional

# Initialize logger
logger = logging.getLogger(__name__)

# Define storage directory
EXTRACTION_RESULTS_DIR = "storage/extraction_results"

# Ensure the directory exists
try:
    os.makedirs(EXTRACTION_RESULTS_DIR, exist_ok=True)
    logger.info(f"Extraction results directory created/verified at: {os.path.abspath(EXTRACTION_RESULTS_DIR)}")
except Exception as e:
    logger.error(f"Error creating extraction results directory: {e}")


def generate_unique_id() -> str:
    """
    Generate a unique ID for an extraction.

    Returns:
        str: A unique ID
    """
    # Combine a timestamp with a UUID to ensure uniqueness
    timestamp = int(time.time())
    random_uuid = str(uuid.uuid4())[:8]
    return f"ext_{timestamp}_{random_uuid}"


def save_extraction_result(extraction_id: str, extraction_result: Dict[str, Any]) -> None:
    """
    Save an extraction result to storage.

    Args:
        extraction_id: Unique ID for the extraction
        extraction_result: The extraction result data
    """
    try:
        # Create the storage directory if it doesn't exist
        os.makedirs(EXTRACTION_RESULTS_DIR, exist_ok=True)

        # Save the extraction result as JSON
        file_path = os.path.join(EXTRACTION_RESULTS_DIR, f"{extraction_id}.json")
        logger.info(f"Saving extraction result to: {file_path}")

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(extraction_result, f, ensure_ascii=False, indent=2)

        # Verify the file was saved
        if os.path.exists(file_path):
            logger.info(f"Successfully saved extraction result: {extraction_id}")
        else:
            logger.error(f"Failed to save extraction result: {extraction_id} (file not found after save)")
    except Exception as e:
        logger.error(f"Error saving extraction result: {e}")
        raise


def get_extraction_result(extraction_id: str) -> Optional[Dict[str, Any]]:
    """
    Retrieve an extraction result from storage.

    Args:
        extraction_id: Unique ID for the extraction

    Returns:
        The extraction result data or None if not found
    """
    try:
        # Get the file path
        file_path = os.path.join(EXTRACTION_RESULTS_DIR, f"{extraction_id}.json")
        logger.info(f"Looking for extraction result at: {file_path}")

        # Check if the file exists
        if not os.path.exists(file_path):
            logger.warning(f"Extraction result not found: {extraction_id}")
            # List available extraction results for debugging
            available_files = os.listdir(EXTRACTION_RESULTS_DIR)
            logger.info(f"Available extraction results: {available_files}")
            return None

        # Load the extraction result
        with open(file_path, 'r', encoding='utf-8') as f:
            result = json.load(f)

        logger.info(f"Retrieved extraction result: {extraction_id}")
        return result
    except Exception as e:
        logger.error(f"Error retrieving extraction result: {e}")
        return None


def list_extraction_results() -> Dict[str, Dict[str, Any]]:
    """
    List all available extraction results.

    Returns:
        Dict mapping extraction IDs to basic metadata
    """
    try:
        results = {}

        # List all JSON files in the storage directory
        for filename in os.listdir(EXTRACTION_RESULTS_DIR):
            if filename.endswith('.json'):
                extraction_id = filename[:-5]  # Remove .json extension

                # Get basic metadata
                file_path = os.path.join(EXTRACTION_RESULTS_DIR, filename)
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # Store basic metadata
                results[extraction_id] = {
                    "id": extraction_id,
                    "timestamp": data.get("timestamp", 0),
                    "filename": data.get("filename", "Unknown"),
                    "page_count": len(data.get("image_mappings", {}))
                }

        return results
    except Exception as e:
        logger.error(f"Error listing extraction results: {e}")
        return {}
