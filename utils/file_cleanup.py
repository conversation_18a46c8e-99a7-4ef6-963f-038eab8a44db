import os
import glob
import logging

# Initialize logger
logger = logging.getLogger(__name__)

def cleanup_temp_files(base_name: str, preserve_images: bool = True):
    """
    Deletes files related to a specific PDF (based on base_name) from temp folders.

    Args:
        base_name: Base name of the PDF file
        preserve_images: If True, images will be preserved for validation
    """
    # Define folders to clean up
    if preserve_images:
        # Skip page_images folder to preserve images for validation
        folders = ["storage/uploads", "storage/page_htmls"]
        logger.info(f"Cleaning up temporary files for {base_name} (preserving images)")
    else:
        # Include all folders
        folders = ["storage/uploads", "storage/page_images", "storage/page_htmls"]
        logger.info(f"Cleaning up all temporary files for {base_name}")

    # Clean up files in each folder
    for folder in folders:
        pattern = os.path.join(folder, f"{base_name}*")
        for file_path in glob.glob(pattern):
            try:
                os.remove(file_path)
                logger.info(f"Deleted: {file_path}")
            except Exception as e:
                logger.warning(f"Failed to delete {file_path}: {e}")
