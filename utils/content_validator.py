"""
Content Extraction Testing and Validation Tool

This module provides a modular tool for validating extracted JSON content against
the original source images using OpenAI's Vision model.

It can be used with any workflow that has:
1. Extracted content in JSON format
2. Original source material in image format
"""

import json
import logging
import base64
from typing import Dict, List, Union, Any, Optional, Tuple
from pathlib import Path

from langchain_core.messages import HumanMessage
from config import llm_config
from llm_manager.llm_factory import LLMFactory

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize LLM factory
llm_factory = LLMFactory(llm_config)


class ContentValidator:
    """
    A modular tool for validating extracted JSON content against original source images.

    This class uses OpenAI's Vision model to compare extracted content with the original
    source images and determine if the extraction was accurate.
    """

    def __init__(self, model: str = "gpt-4.1-mini"):
        """
        Initialize the ContentValidator.

        Args:
            model: The OpenAI model to use for validation (default: "gpt-4.1-mini")
        """
        self.llm = llm_factory.get_llm("openai_extraction", model)

    def encode_image(self, image_path: str) -> str:
        """
        Encode an image to base64.

        Args:
            image_path: Path to the image file

        Returns:
            Base64 encoded image string
        """
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode("utf-8")

    def construct_validation_prompt(self, extracted_content: Dict[str, Any]) -> str:
        """
        Construct a prompt for validating extracted content.

        Args:
            extracted_content: The extracted content in JSON format

        Returns:
            A prompt string for the validation task
        """
        page_number = extracted_content.get("page_number", "Unknown")
        extracted_text = extracted_content.get("extracted_text", "")

        prompt = f"""
        You are a content validation expert. Your task is to verify if the extracted text matches exactly what is shown in the image.

        This is page {page_number} of a document.

        Here is the extracted text:
        ```
        {extracted_text}
        ```

        Please analyze the image carefully and compare it with the extracted text. Check for:
        1. Missing content
        2. Incorrect text
        3. Misinterpreted structure
        4. Any other discrepancies

        Respond with a JSON object in the following format:
        ```json
        {{
            "content_match": true/false,
            "mismatches": [
                {{
                    "field": "line_number or description",
                    "extracted": "what was extracted",
                    "actual": "what is actually in the image"
                }}
            ]
        }}
        ```

        If there are no mismatches, set "content_match" to true and provide an empty "mismatches" array.
        Be extremely thorough and precise in your validation.
        """
        return prompt

    def construct_input_content(self, prompt: str, image_path: str) -> List[Dict[str, Union[str, Dict[str, str]]]]:
        """
        Construct the input content for the LLM.

        Args:
            prompt: The validation prompt
            image_path: Path to the image file

        Returns:
            A list of content items for the LLM
        """
        base64_image = self.encode_image(image_path)

        content = [
            {
                "type": "text",
                "text": prompt
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{base64_image}",
                    "detail": "high"
                }
            }
        ]

        return content

    def parse_validation_result(self, response: str) -> Dict[str, Any]:
        """
        Parse the validation result from the LLM response.

        Args:
            response: The LLM response string

        Returns:
            A dictionary containing the validation result
        """
        try:
            # Extract JSON from the response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1

            if json_start == -1 or json_end == 0:
                # If no JSON is found, create a default response
                logger.warning("No JSON found in LLM response, using default response")
                return {
                    "content_match": False,
                    "mismatches": [
                        {
                            "field": "response_format",
                            "extracted": "N/A",
                            "actual": "Invalid response format from LLM"
                        }
                    ]
                }

            json_str = response[json_start:json_end]
            result = json.loads(json_str)

            # Ensure the result has the expected structure
            if "content_match" not in result:
                result["content_match"] = False

            if "mismatches" not in result:
                result["mismatches"] = []

            return result

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LLM response as JSON: {e}")
            return {
                "content_match": False,
                "mismatches": [
                    {
                        "field": "response_format",
                        "extracted": "N/A",
                        "actual": f"Invalid JSON in LLM response: {e}"
                    }
                ]
            }
        except Exception as e:
            logger.error(f"Error parsing validation result: {e}")
            return {
                "content_match": False,
                "mismatches": [
                    {
                        "field": "processing_error",
                        "extracted": "N/A",
                        "actual": f"Error processing validation: {str(e)}"
                    }
                ]
            }

    def validate_content(self, extracted_content: Dict[str, Any], image_path: str) -> Dict[str, Any]:
        """
        Validate extracted content against an image.

        Args:
            extracted_content: The extracted content in JSON format
            image_path: Path to the image file

        Returns:
            A dictionary containing the validation result
        """
        try:
            # Check if image file exists
            if not Path(image_path).exists():
                return {
                    "content_match": False,
                    "mismatches": [
                        {
                            "field": "image_path",
                            "extracted": image_path,
                            "actual": "Image file does not exist"
                        }
                    ]
                }

            # Check if extracted content has the expected structure
            if "page_number" not in extracted_content or "extracted_text" not in extracted_content:
                return {
                    "content_match": False,
                    "mismatches": [
                        {
                            "field": "content_structure",
                            "extracted": str(list(extracted_content.keys())),
                            "actual": "Expected keys 'page_number' and 'extracted_text'"
                        }
                    ]
                }

            # Construct the validation prompt
            prompt = self.construct_validation_prompt(extracted_content)

            # Construct the input content
            content = self.construct_input_content(prompt, image_path)

            # Call the LLM
            response = self.llm.invoke([
                HumanMessage(
                    content=content,
                    additional_kwargs={"tool_choice": "vision"}
                )
            ])

            # Parse the validation result
            result = self.parse_validation_result(response.content)

            return result

        except Exception as e:
            logger.error(f"Error validating content: {e}")
            return {
                "content_match": False,
                "mismatches": [
                    {
                        "field": "validation_error",
                        "extracted": "N/A",
                        "actual": f"Error during validation: {str(e)}"
                    }
                ]
            }

    def validate_multiple_contents(self,
                                  extracted_contents: List[Dict[str, Any]],
                                  image_paths: List[str]) -> List[Dict[str, Any]]:
        """
        Validate multiple extracted contents against multiple images.

        Args:
            extracted_contents: List of extracted contents in JSON format
            image_paths: List of paths to the image files

        Returns:
            A list of dictionaries containing the validation results
        """
        if len(extracted_contents) != len(image_paths):
            logger.error(f"Number of extracted contents ({len(extracted_contents)}) does not match number of images ({len(image_paths)})")
            return [{
                "content_match": False,
                "mismatches": [
                    {
                        "field": "input_mismatch",
                        "extracted": f"{len(extracted_contents)} content items",
                        "actual": f"{len(image_paths)} image paths"
                    }
                ]
            }]

        results = []
        for content, image_path in zip(extracted_contents, image_paths):
            result = self.validate_content(content, image_path)
            results.append(result)

        return results

    def validate_batch(self,
                      extracted_content: Dict[str, Any],
                      image_paths: List[str],
                      content_to_image_mapping: Optional[Dict[str, int]] = None) -> Dict[str, Any]:
        """
        Validate a batch of extracted content against multiple images.

        Args:
            extracted_content: The extracted content in JSON format (may contain multiple items)
            image_paths: List of paths to the image files
            content_to_image_mapping: Optional mapping from content keys to image indices

        Returns:
            A dictionary containing the validation results
        """
        # If no mapping is provided, validate the entire content against each image
        if content_to_image_mapping is None:
            results = []
            for image_path in image_paths:
                result = self.validate_content(extracted_content, image_path)
                results.append(result)

            # Aggregate results
            all_match = all(result["content_match"] for result in results)
            all_mismatches = []
            for i, result in enumerate(results):
                for mismatch in result.get("mismatches", []):
                    mismatch["image_index"] = i
                    all_mismatches.append(mismatch)

            return {
                "content_match": all_match,
                "mismatches": all_mismatches,
                "image_results": results
            }

        # If mapping is provided, validate each content item against its corresponding image
        else:
            results = {}
            all_match = True
            all_mismatches = []

            for content_key, image_index in content_to_image_mapping.items():
                if image_index >= len(image_paths):
                    logger.error(f"Image index {image_index} out of range (only {len(image_paths)} images available)")
                    all_match = False
                    all_mismatches.append({
                        "field": f"mapping.{content_key}",
                        "extracted": f"Image index {image_index}",
                        "actual": f"Only {len(image_paths)} images available"
                    })
                    continue

                # Extract the content item
                content_item = extracted_content.get(content_key, {})
                if not content_item:
                    logger.error(f"Content key '{content_key}' not found in extracted content")
                    all_match = False
                    all_mismatches.append({
                        "field": f"content.{content_key}",
                        "extracted": "Not found",
                        "actual": "Expected content item"
                    })
                    continue

                # Validate the content item
                result = self.validate_content(content_item, image_paths[image_index])
                results[content_key] = result

                if not result["content_match"]:
                    all_match = False
                    for mismatch in result.get("mismatches", []):
                        mismatch["content_key"] = content_key
                        mismatch["image_index"] = image_index
                        all_mismatches.append(mismatch)

            return {
                "content_match": all_match,
                "mismatches": all_mismatches,
                "item_results": results
            }


# Example usage
if __name__ == "__main__":
    # Example extracted content
    example_content = {
        "question_number": 1,
        "question_text": "What is the capital of France?",
        "options": [
            {"text": "London", "identifier": "1"},
            {"text": "Paris", "identifier": "2"},
            {"text": "Berlin", "identifier": "3"},
            {"text": "Madrid", "identifier": "4"}
        ],
        "correct_answer": "2"
    }

    # Example image path
    example_image_path = "path/to/image.png"

    # Create validator
    validator = ContentValidator()

    # Validate content
    result = validator.validate_content(example_content, example_image_path)

    # Print result
    print(json.dumps(result, indent=2))
