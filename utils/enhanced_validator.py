"""
Enhanced Content Validator

This module provides an enhanced validator that can:
1. Validate extracted content against source images
2. Fix extraction errors automatically
3. Handle multiple languages
4. Process images in parallel
"""

import os
import json
import base64
import logging
import threading
import time
import concurrent.futures
from typing import Dict, List, Any, Optional, Tuple, Callable
from pathlib import Path
from queue import Queue

# Import OpenAI client directly
from openai import OpenAI
import config
from llm_manager.prompts import html_content_validation_prompt, extracted_question_validation_prompt

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize OpenAI client
openai_client = OpenAI(api_key=config.OPENAI_API_KEY_ADMIN)


class EnhancedValidator:
    """
    Enhanced validator for content extraction with automatic error correction.

    This class validates extracted content against source images and automatically
    fixes any errors found during validation.
    """

    def __init__(self, model: str = "gpt-4.1-mini", max_workers: int = 3, use_parallel: bool = False):
        """
        Initialize the EnhancedValidator.

        Args:
            model: The OpenAI model to use for validation and correction
            max_workers: Maximum number of parallel workers for processing
            use_parallel: Whether to use parallel processing (True) or serial processing (False)
        """
        self.model = model
        self.client = openai_client
        self.max_workers = max_workers
        self.use_parallel = use_parallel
        logger.info(f"Initialized EnhancedValidator with model={model}, max_workers={max_workers}, use_parallel={use_parallel}")

    def encode_image(self, image_path: str) -> str:
        """
        Encode an image to base64.

        Args:
            image_path: Path to the image file

        Returns:
            Base64 encoded image string
        """
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode("utf-8")
        except Exception as e:
            logger.error(f"Error encoding image {image_path}: {str(e)}")
            raise

    def construct_validation_prompt(self, extracted_content: Dict[str, Any], language: str = "english") -> str:
        """
        Construct a prompt for validating and fixing extracted content.

        Args:
            extracted_content: The extracted content in JSON format
            language: The language of the content

        Returns:
            A prompt string for the validation and correction task
        """
        page_number = extracted_content.get("page_number", "Unknown")
        extracted_text = extracted_content.get("extracted_text", "")

        prompt = f"""
        You are a content validation and correction expert. Your task is to verify if the extracted text matches exactly what is shown in the image and fix any errors.

        This is page {page_number} of a document.

        Here is the extracted text:
        ```
        {extracted_text}
        ```

        Please analyze the image carefully and compare it with the extracted text. Check for:
        1. Missing content
        2. Incorrect text
        3. Spelling mistakes
        4. Grammar mistakes
        5. Misinterpreted structure

        IMPORTANT GUIDELINES:
        - Mathematical formulas should be in LaTeX format. If the extracted text has formulas in LaTeX, verify they represent the same mathematical expression as in the image, even if the LaTeX syntax differs slightly.
        - Ignore extra spaces or extra newlines when comparing.
        - Maintain the original formatting structure as much as possible.
        - For non-English text, ensure proper character encoding and language-specific symbols.

        Respond with a JSON object in the following format:
        ```json
        {{
            "content_match": true/false,
            "mismatches": [
                {{
                    "field": "line_number or description",
                    "extracted": "what was extracted",
                    "actual": "what is actually in the image"
                }}
            ],
            "corrected_text": "the full corrected text with all errors fixed"
        }}
        ```

        If there are no mismatches, set "content_match" to true, provide an empty "mismatches" array, and set "corrected_text" to the original extracted text.
        Be extremely thorough and precise in your validation.
        """
        return prompt

    def construct_messages(self, prompt: str, image_path: str) -> List[Dict[str, Any]]:
        """
        Construct the messages for the OpenAI API.

        Args:
            prompt: The validation prompt
            image_path: Path to the image file

        Returns:
            A list of message objects for the OpenAI API
        """
        base64_image = self.encode_image(image_path)

        # Format for OpenAI's direct API
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{base64_image}",
                            "detail": "high"
                        }
                    }
                ]
            }
        ]

        return messages

    def parse_validation_result(self, response: str) -> Dict[str, Any]:
        """
        Parse the validation result from the LLM response.

        Args:
            response: The LLM response string

        Returns:
            A dictionary containing the validation result
        """
        try:
            # Extract JSON from the response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1

            if json_start == -1 or json_end == 0:
                # If no JSON is found, create a default response
                logger.warning("No JSON found in LLM response, using default response")
                return {
                    "content_match": False,
                    "mismatches": [
                        {
                            "field": "response_format",
                            "extracted": "N/A",
                            "actual": "Invalid response format from LLM"
                        }
                    ],
                    "corrected_text": ""
                }

            json_str = response[json_start:json_end]
            result = json.loads(json_str)

            # Ensure the result has the expected structure
            if "content_match" not in result:
                result["content_match"] = False

            if "mismatches" not in result:
                result["mismatches"] = []

            if "corrected_text" not in result:
                result["corrected_text"] = ""

            return result

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LLM response as JSON: {e}")
            return {
                "content_match": False,
                "mismatches": [
                    {
                        "field": "response_format",
                        "extracted": "N/A",
                        "actual": f"Invalid JSON in LLM response: {e}"
                    }
                ],
                "corrected_text": ""
            }
        except Exception as e:
            logger.error(f"Error parsing validation result: {e}")
            return {
                "content_match": False,
                "mismatches": [
                    {
                        "field": "processing_error",
                        "extracted": "N/A",
                        "actual": f"Error processing validation: {str(e)}"
                    }
                ],
                "corrected_text": ""
            }

    def validate_and_fix_content(self, extracted_content: Dict[str, Any], image_path: str, language: str = "english") -> Dict[str, Any]:
        """
        Validate extracted content against an image and fix any errors.

        Args:
            extracted_content: The extracted content in JSON format
            image_path: Path to the image file
            language: The language of the content

        Returns:
            A dictionary containing the validation result and corrected content
        """
        try:
            # Check if image file exists
            if not os.path.exists(image_path):
                logger.warning(f"Image file not found at original path: {image_path}")

                # Try to find the image in the page_images directory
                base_name = os.path.basename(image_path)
                alternative_path = os.path.join("storage/page_images", base_name)

                if os.path.exists(alternative_path):
                    logger.info(f"Found image at alternative path: {alternative_path}")
                    image_path = alternative_path
                else:
                    logger.error(f"Image file not found at alternative path either: {alternative_path}")
                    return {
                        "content_match": False,
                        "mismatches": [
                            {
                                "field": "image_path",
                                "extracted": image_path,
                                "actual": "Image file not found"
                            }
                        ],
                        "corrected_text": ""
                    }

            # Check if extracted content has the expected structure
            if "page_number" not in extracted_content or "extracted_text" not in extracted_content:
                return {
                    "content_match": False,
                    "mismatches": [
                        {
                            "field": "content_structure",
                            "extracted": str(list(extracted_content.keys())),
                            "actual": "Expected keys 'page_number' and 'extracted_text'"
                        }
                    ],
                    "corrected_text": ""
                }

            # Get the appropriate validation prompt based on the content type
            page_number = str(extracted_content.get("page_number", "Unknown"))

            # Determine if this is HTML content or question content based on the structure
            extracted_text = extracted_content.get("extracted_text", "")
            is_html = "<" in extracted_text and ">" in extracted_text

            if is_html:
                # Use HTML validation prompt
                prompt = html_content_validation_prompt(page_number)
            else:
                # Use question validation prompt
                prompt = extracted_question_validation_prompt(page_number)

            # Log the prompt being used for debugging
            logger.info(f"Using validation prompt for page {page_number} (is_html={is_html}):\n{prompt[:200]}...")

            # Construct the messages
            messages = self.construct_messages(prompt, image_path)

            # Call the OpenAI API with timeout handling
            logger.info(f"Calling OpenAI API for validation and correction of page {extracted_content.get('page_number', 'Unknown')}")

            # Set a timeout for the API call (2 minutes)
            timeout_seconds = 120
            logger.info(f"Setting API call timeout to {timeout_seconds} seconds")

            # Use a separate thread with timeout
            def call_openai_with_timeout():
                try:
                    start_time = time.time()
                    # Make the API call
                    response = self.client.chat.completions.create(
                        model=self.model,
                        messages=messages,
                        temperature=0
                    )
                    elapsed = time.time() - start_time
                    logger.info(f"OpenAI API call completed in {elapsed:.2f} seconds")
                    return response
                except Exception as e:
                    logger.error(f"Error in OpenAI API call: {str(e)}")
                    raise

            # Create and start the thread
            import threading
            import queue

            result_queue = queue.Queue()
            def thread_target():
                try:
                    result = call_openai_with_timeout()
                    result_queue.put((True, result))
                except Exception as e:
                    result_queue.put((False, e))

            thread = threading.Thread(target=thread_target)
            thread.daemon = True
            thread.start()

            # Wait for the thread to complete or timeout
            try:
                success, result = result_queue.get(timeout=timeout_seconds)
                if not success:
                    raise result
                response = result
                logger.info("Successfully received response from OpenAI API")
            except queue.Empty:
                logger.error(f"OpenAI API call timed out after {timeout_seconds} seconds")
                raise TimeoutError(f"OpenAI API call timed out after {timeout_seconds} seconds")

            # Parse the validation result
            result = self.parse_validation_result(response.choices[0].message.content)

            # Add page information to the result
            result["page_number"] = extracted_content.get("page_number", "Unknown")

            # Log the result
            if result["content_match"]:
                logger.info(f"Content matches for page {result['page_number']}")
            else:
                logger.info(f"Found {len(result['mismatches'])} mismatches for page {result['page_number']}")

            return result

        except TimeoutError as e:
            logger.error(f"Timeout error validating and fixing content: {e}")
            return {
                "content_match": False,
                "mismatches": [
                    {
                        "field": "validation_timeout",
                        "extracted": "N/A",
                        "actual": f"Validation timed out: {str(e)}"
                    }
                ],
                "corrected_text": extracted_content.get("extracted_text", ""),  # Use original text if timeout
                "page_number": extracted_content.get("page_number", "Unknown")
            }
        except Exception as e:
            logger.error(f"Error validating and fixing content: {e}")
            return {
                "content_match": False,
                "mismatches": [
                    {
                        "field": "validation_error",
                        "extracted": "N/A",
                        "actual": f"Error during validation: {str(e)}"
                    }
                ],
                "corrected_text": extracted_content.get("extracted_text", ""),  # Use original text if error
                "page_number": extracted_content.get("page_number", "Unknown")
            }

    def process_image(self, image_path: str, page_number: int, extract_func: Callable, language: str = "english") -> Dict[str, Any]:
        """
        Process a single image: extract content, validate, and fix errors.

        Args:
            image_path: Path to the image file
            page_number: Page number
            extract_func: Function to extract content from the image
            language: Language of the content

        Returns:
            A dictionary containing the processed result
        """
        try:
            logger.info(f"Processing image {page_number}: {image_path}")

            # Extract content from the image
            logger.info(f"Extracting content from image {page_number}")
            extracted_text = extract_func(image_path, page_number)

            # Create extracted content object
            extracted_content = {
                "page_number": page_number,
                "extracted_text": extracted_text
            }

            # Validate and fix the extracted content
            logger.info(f"Validating and fixing content for page {page_number}")
            result = self.validate_and_fix_content(extracted_content, image_path, language)

            # Return the result
            return {
                "page_number": page_number,
                "image_path": image_path,
                "extracted_text": extracted_text,
                "validation_result": result,
                "corrected_text": result.get("corrected_text", ""),
                "content_match": result.get("content_match", False),
                "mismatches": result.get("mismatches", [])
            }

        except Exception as e:
            logger.error(f"Error processing image {page_number}: {e}")
            return {
                "page_number": page_number,
                "image_path": image_path,
                "extracted_text": "",
                "validation_result": {
                    "content_match": False,
                    "mismatches": [
                        {
                            "field": "processing_error",
                            "extracted": "N/A",
                            "actual": f"Error processing image: {str(e)}"
                        }
                    ],
                    "corrected_text": ""
                },
                "corrected_text": "",
                "content_match": False,
                "mismatches": [
                    {
                        "field": "processing_error",
                        "extracted": "N/A",
                        "actual": f"Error processing image: {str(e)}"
                    }
                ]
            }

    def process_images_serial(self, image_paths: List[str], extract_func: Callable, language: str = "english") -> Dict[str, Any]:
        """
        Process multiple images serially (one at a time).

        Args:
            image_paths: List of paths to the image files
            extract_func: Function to extract content from images
            language: Language of the content

        Returns:
            A dictionary containing the processed results
        """
        total_pages = len(image_paths)
        logger.info(f"Processing {total_pages} images serially (one at a time)")

        results = {}
        all_mismatches = []
        content_match = True

        # Process each image one at a time
        for i, path in enumerate(image_paths):
            page_number = i + 1
            page_key = f"page_{page_number}"

            logger.info(f"Processing page {page_number}/{total_pages}")
            start_time = time.time()

            try:
                # Process the image
                result = self.process_image(path, page_number, extract_func, language)
                results[page_key] = result

                # Update overall content match status
                if not result["content_match"]:
                    content_match = False

                # Collect mismatches
                for mismatch in result["mismatches"]:
                    mismatch["page"] = page_key
                    all_mismatches.append(mismatch)

                elapsed = time.time() - start_time
                logger.info(f"✅ Page {page_number}/{total_pages} completed in {elapsed:.2f} seconds")

            except Exception as e:
                logger.error(f"Error processing page {page_number}: {e}")
                results[page_key] = {
                    "page_number": page_number,
                    "error": str(e),
                    "content_match": False,
                    "mismatches": [],
                    "extracted_text": "",
                    "corrected_text": ""
                }
                content_match = False

        # Return the overall result
        return {
            "content_match": content_match,
            "mismatches": all_mismatches,
            "page_results": results,
            "page_count": total_pages
        }

    def process_images(self, image_paths: List[str], extract_func: Callable, language: str = "english", timeout: int = 300) -> Dict[str, Any]:
        """
        Process multiple images using either parallel or serial processing based on the use_parallel flag.

        Args:
            image_paths: List of paths to the image files
            extract_func: Function to extract content from images
            language: Language of the content
            timeout: Maximum time in seconds to wait for a single page processing (default: 5 minutes)

        Returns:
            A dictionary containing the processed results
        """
        if self.use_parallel:
            logger.info("Using parallel processing mode")
            return self.process_images_parallel(image_paths, extract_func, language, timeout)
        else:
            logger.info("Using serial processing mode")
            return self.process_images_serial(image_paths, extract_func, language)

    def process_images_parallel(self, image_paths: List[str], extract_func: Callable, language: str = "english", timeout: int = 120) -> Dict[str, Any]:
        """
        Process multiple images in parallel with improved progress tracking and error handling.
        Includes retry mechanism for failed pages and ability to continue despite failures.

        Args:
            image_paths: List of paths to the image files
            extract_func: Function to extract content from images
            language: Language of the content
            timeout: Maximum time in seconds to wait for a single page processing (default: 5 minutes)

        Returns:
            A dictionary containing the processed results
        """
        total_pages = len(image_paths)
        logger.info(f"Processing {total_pages} images in parallel with {self.max_workers} workers")

        results = {}
        all_mismatches = []
        content_match = True
        completed_pages = 0
        failed_pages = []

        # Shorter timeout for initial processing
        initial_timeout = min(timeout, 90)  # 1.5 minutes for initial attempt
        logger.info(f"Setting initial timeout to {initial_timeout} seconds per page")

        # Create a queue for tracking progress
        progress_queue = Queue()

        def process_with_progress(path, page_num, extract_func, language):
            """Wrapper function to track progress"""
            try:
                start_time = time.time()
                logger.info(f"Starting processing of page {page_num}")

                # Process the image
                result = self.process_image(path, page_num, extract_func, language)

                # Report completion
                elapsed = time.time() - start_time
                progress_queue.put((page_num, True, result, elapsed))
                return result
            except Exception as e:
                logger.error(f"Error in worker thread for page {page_num}: {str(e)}")
                progress_queue.put((page_num, False, str(e), 0))
                raise

        # Start a thread to monitor and report progress
        def progress_monitor():
            while completed_pages < total_pages:
                try:
                    page_num, success, result, elapsed = progress_queue.get(timeout=1)
                    if success:
                        logger.info(f"✅ Page {page_num}/{total_pages} completed in {elapsed:.2f} seconds")
                    else:
                        logger.error(f"❌ Page {page_num}/{total_pages} failed: {result}")
                    progress_queue.task_done()
                except Exception:
                    # Just a timeout or queue empty, continue waiting
                    pass

        # Start the progress monitor thread
        monitor_thread = threading.Thread(target=progress_monitor, daemon=True)
        monitor_thread.start()

        # Process images in smaller batches to avoid memory issues
        batch_size = min(self.max_workers * 2, total_pages)  # Process 2x max_workers at a time
        for batch_start in range(0, total_pages, batch_size):
            batch_end = min(batch_start + batch_size, total_pages)
            batch = image_paths[batch_start:batch_end]
            batch_indices = list(range(batch_start, batch_end))

            logger.info(f"Processing batch of {len(batch)} pages (pages {batch_start+1}-{batch_end})")

            # Process batch in parallel
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit tasks
                future_to_page = {
                    executor.submit(process_with_progress, path, i + 1, extract_func, language): i + 1
                    for i, path in zip(batch_indices, batch)
                }

                # Process results as they complete
                for future in concurrent.futures.as_completed(future_to_page):
                    page_number = future_to_page[future]
                    try:
                        # Get result with shorter timeout to prevent hanging
                        result = future.result(timeout=initial_timeout)
                        results[f"page_{page_number}"] = result

                        # Update overall content match status
                        if not result["content_match"]:
                            content_match = False

                        # Collect mismatches
                        for mismatch in result["mismatches"]:
                            mismatch["page"] = f"page_{page_number}"
                            all_mismatches.append(mismatch)

                        completed_pages += 1
                        logger.info(f"Processed {completed_pages}/{total_pages} pages")

                    except concurrent.futures.TimeoutError:
                        logger.warning(f"⚠️ Timeout processing page {page_number} (exceeded {initial_timeout} seconds) - will retry later")
                        # Add to failed pages for retry
                        failed_pages.append((page_number, image_paths[page_number-1]))
                        # Mark as completed for this round
                        completed_pages += 1
                    except Exception as e:
                        logger.error(f"Error processing page {page_number}: {e}")
                        # Add to failed pages for retry
                        failed_pages.append((page_number, image_paths[page_number-1]))
                        # Mark as completed for this round
                        completed_pages += 1

            # Give a brief pause between batches to allow resources to be freed
            time.sleep(1)

        # Retry failed pages one by one with longer timeout
        if failed_pages:
            logger.info(f"Retrying {len(failed_pages)} failed pages with longer timeout ({timeout} seconds)")

            for page_number, image_path in failed_pages:
                logger.info(f"Retrying page {page_number} with longer timeout")
                try:
                    # Process the page with longer timeout
                    result = self.process_image(image_path, page_number, extract_func, language)
                    results[f"page_{page_number}"] = result

                    # Update overall content match status
                    if not result["content_match"]:
                        content_match = False

                    # Collect mismatches
                    for mismatch in result["mismatches"]:
                        mismatch["page"] = f"page_{page_number}"
                        all_mismatches.append(mismatch)

                    logger.info(f"✅ Successfully processed page {page_number} on retry")
                except Exception as e:
                    logger.error(f"❌ Failed to process page {page_number} even on retry: {e}")
                    results[f"page_{page_number}"] = {
                        "page_number": page_number,
                        "error": f"Failed after retry: {str(e)}",
                        "content_match": False,
                        "mismatches": [],
                        "extracted_text": "",
                        "corrected_text": ""
                    }
                    content_match = False

        # Check if any pages are still missing from results
        for i in range(1, total_pages + 1):
            page_key = f"page_{i}"
            if page_key not in results:
                logger.error(f"Page {i} is missing from results - adding error placeholder")
                results[page_key] = {
                    "page_number": i,
                    "error": "Page processing failed",
                    "content_match": False,
                    "mismatches": [],
                    "extracted_text": "",
                    "corrected_text": ""
                }

        # Return the overall result
        return {
            "content_match": content_match,
            "mismatches": all_mismatches,
            "page_results": results,
            "page_count": total_pages,
            "failed_pages": [p[0] for p in failed_pages if f"page_{p[0]}" not in results or "error" in results[f"page_{p[0]}"]]
        }
