from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class PromptCreate(BaseModel):
    """
    Pydantic model for creating a new prompt.

    Attributes:
        prompt (str): The prompt text
        promptCategory (str): The category of the prompt (ibookgpt or gptsir)
        promptLabel (str): The label of the prompt
        promptDescription (Optional[str]): The description of the prompt
    """
    prompt: str
    promptCategory: str
    promptLabel: str
    promptDescription: Optional[str] = None

class PromptUpdate(BaseModel):
    """
    Pydantic model for updating an existing prompt.

    Attributes:
        prompt (str): The prompt text
        promptCategory (str): The category of the prompt (ibookgpt or gptsir)
        promptLabel (str): The label of the prompt
        promptDescription (Optional[str]): The description of the prompt
    """
    prompt: str
    promptCategory: str
    promptLabel: str
    promptDescription: Optional[str] = None

class PromptResponse(BaseModel):
    """
    Pydantic model for a prompt response.

    Attributes:
        id (int): The ID of the prompt
        prompt (str): The prompt text
        promptCategory (str): The category of the prompt (ibookgpt or gptsir)
        promptLabel (str): The label of the prompt
        promptDescription (Optional[str]): The description of the prompt
        version (int): The version of the prompt
    """
    id: int
    prompt: str
    promptCategory: str
    promptLabel: str
    promptDescription: Optional[str] = None
    version: int = 0
