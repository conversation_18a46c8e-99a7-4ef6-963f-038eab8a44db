from typing import List, Union, Callable, Optional, Any, Dict
from langchain_core.messages import HumanMessage
import logging
import time
from typing import List, Union, Callable, Optional, Any, Dict
from openai import OpenAI
import config

# Initialize logger
logger = logging.getLogger(__name__)


class ContentExtractor:
    def __init__(self, llm=None, model="gpt-4.1-mini"):
        """
        Args:
            llm: Legacy LLM instance (optional, for backward compatibility)
            model: OpenAI model to use if llm is not provided
        """
        self.llm = llm
        self.model = model
        self.client = OpenAI(api_key=config.OPENAI_API_KEY_ADMIN)


    @staticmethod
    def parse_llm_result(response: str) -> str:
        """
        Clean LLM response (e.g., remove markdown fences) and return raw content.

        Args:
            response (str): Raw LLM string response

        Returns:
            str: Cleaned response string
        """
        try:
            if response.startswith("```json") or response.startswith("```html"):
                response = response[7:]
            elif response.startswith("html"):
                response = response[4:]
            elif response.startswith("```"):
                response = response[3:]
            if response.endswith("```"):
                response = response[:-3]
            return response.strip()
        except Exception as e:
            raise ValueError(f"Failed to parse LLM response: {str(e)}")


    def extract_content_from_images(
        self,
        content: List[Union[dict[str, str], dict[str, Union[str, dict[str, str]]]]],
        parser_func: Optional[Callable[[str], str]] = None,
        context_info: Optional[Dict[str, str]] = None,
    ) -> Any:
        """
        Generic method to send messages to the LLM and extract content.

        Args:
            content (List[dict]): List of message objects including prompt and image dicts.
            parser_func (Callable): Optional function to post-process the LLM output.
            context_info (Dict): Optional context information for error reporting (e.g., page/column numbers).

        Returns:
            str: Parsed or raw LLM output, or a special error response for 504 errors
        """
        import logging
        logger = logging.getLogger(__name__)

        # Create context description for error messages
        context_desc = ""
        if context_info:
            if context_info.get("page") and context_info.get("column"):
                context_desc = f"page {context_info['page']} column {context_info['column']}"
            elif context_info.get("page"):
                context_desc = f"page {context_info['page']}"
            elif context_info.get("description"):
                context_desc = context_info['description']

        try:
            response = self.llm.invoke([
                HumanMessage(
                    content=content,
                    additional_kwargs={"tool_choice": "vision"}
                )
            ])
            return parser_func(response.content) if parser_func else response

        except Exception as retry_error:
            error_details = str(retry_error)
            logger.error(f"Retry also failed after timeout error for {context_desc}: {error_details}")
            # Instead of raising the error, return a special error response
            # that can be handled by the calling code
            from langchain_core.messages import AIMessage
            error_msg = f"SKIPPED_DUE_TO_504: {context_desc} has been skipped because OpenAI returned 504 Gateway Timeout"
            return AIMessage(content=error_msg, additional_kwargs={
                "skip_due_to_504": True,
                "context": context_desc,
                "error_details": error_details
            })
