from fastapi import API<PERSON>out<PERSON>, Depends, Request, HTTPException, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import <PERSON><PERSON>2Templates
from auth.dependencies import require_login
from auth.rbac import require_roles
from db_config.gpt_prompts_db import (
    get_all_prompts,
    get_prompt_by_id,
    create_prompt,
    update_prompt,
    check_table_structure
)
from models.gpt_prompts_models import PromptCreate, PromptUpdate
import logging

logger = logging.getLogger(__name__)

# Initialize the router
router = APIRouter(prefix="/prompts", tags=["prompts"])

# Initialize the templates
templates = Jinja2Templates(directory="web/templates")

@router.get("/", response_class=HTMLResponse)
async def list_prompts(
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    List all prompts
    """
    if login_check:
        return login_check

    try:
        # Check the table structure
        table_exists = check_table_structure()

        if not table_exists:
            return templates.TemplateResponse(
                "prompts/list.html",
                {"request": request, "prompts": [], "error": "The gptprompts table does not exist or cannot be accessed."}
            )

        # Get all prompts
        prompts = get_all_prompts()

        # Render the template
        return templates.TemplateResponse(
            "prompts/list.html",
            {"request": request, "prompts": prompts}
        )
    except Exception as e:
        logger.error(f"Error listing prompts: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/create", response_class=HTMLResponse)
async def create_prompt_form(
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Show the form for creating a new prompt
    """
    if login_check:
        return login_check

    return templates.TemplateResponse(
        "prompts/create.html",
        {"request": request}
    )

@router.post("/create", response_class=HTMLResponse)
async def create_prompt_submit(
    request: Request,
    prompt: str = Form(...),
    promptCategory: str = Form(...),
    promptLabel: str = Form(...),
    promptDescription: str = Form(""),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Handle the form submission for creating a new prompt
    """
    if login_check:
        return login_check

    try:
        # Format the prompt label (all caps and replace spaces with underscores)
        formatted_prompt_label = promptLabel.upper().replace(" ", "_")

        # Create the prompt data
        prompt_data = {
            "prompt": prompt,
            "promptCategory": promptCategory,  # This will be mapped to prompt_category in the DB function
            "promptLabel": formatted_prompt_label,  # This will be mapped to prompt_label in the DB function
            "promptDescription": promptDescription  # This will be mapped to prompt_description in the DB function
        }

        # Create the prompt
        prompt_id = create_prompt(prompt_data)

        # Redirect to the prompts list
        return RedirectResponse(url="/prompts/", status_code=303)
    except Exception as e:
        logger.error(f"Error creating prompt: {e}")
        return templates.TemplateResponse(
            "prompts/create.html",
            {
                "request": request,
                "error": str(e),
                "prompt": prompt,
                "promptCategory": promptCategory,
                "promptLabel": promptLabel,
                "promptDescription": promptDescription
            }
        )

@router.get("/view/{prompt_id}", response_class=HTMLResponse)
async def view_prompt(
    request: Request,
    prompt_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    View a prompt
    """
    if login_check:
        return login_check

    try:
        # Get the prompt
        prompt = get_prompt_by_id(prompt_id)

        if not prompt:
            raise HTTPException(status_code=404, detail="Prompt not found")

        # Render the template
        return templates.TemplateResponse(
            "prompts/view.html",
            {"request": request, "prompt": prompt}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error viewing prompt: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/edit/{prompt_id}", response_class=HTMLResponse)
async def edit_prompt_form(
    request: Request,
    prompt_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Show the form for editing a prompt
    """
    if login_check:
        return login_check

    try:
        # Get the prompt
        prompt = get_prompt_by_id(prompt_id)

        if not prompt:
            raise HTTPException(status_code=404, detail="Prompt not found")

        # Render the template
        return templates.TemplateResponse(
            "prompts/edit.html",
            {"request": request, "prompt": prompt}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error editing prompt: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/edit/{prompt_id}", response_class=HTMLResponse)
async def edit_prompt_submit(
    request: Request,
    prompt_id: int,
    prompt: str = Form(...),
    promptCategory: str = Form(...),
    promptLabel: str = Form(...),
    promptDescription: str = Form(""),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Handle the form submission for editing a prompt
    """
    if login_check:
        return login_check

    try:
        # Format the prompt label (all caps and replace spaces with underscores)
        formatted_prompt_label = promptLabel.upper().replace(" ", "_")

        # Create the prompt data
        prompt_data = {
            "prompt": prompt,
            "promptCategory": promptCategory,  # This will be mapped to prompt_category in the DB function
            "promptLabel": formatted_prompt_label,  # This will be mapped to prompt_label in the DB function
            "promptDescription": promptDescription  # This will be mapped to prompt_description in the DB function
        }

        # Update the prompt
        success = update_prompt(prompt_id, prompt_data)

        if not success:
            raise HTTPException(status_code=404, detail="Prompt not found")

        # Redirect to the prompts list
        return RedirectResponse(url="/prompts/", status_code=303)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating prompt: {e}")
        return templates.TemplateResponse(
            "prompts/edit.html",
            {
                "request": request,
                "error": str(e),
                "prompt": {
                    "id": prompt_id,
                    "prompt": prompt,
                    "prompt_category": promptCategory,
                    "prompt_label": promptLabel.upper().replace(" ", "_"),  # Format consistently
                    "prompt_description": promptDescription
                }
            }
        )
