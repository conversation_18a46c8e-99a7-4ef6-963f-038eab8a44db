"""
API routes for the PYQs Admin module.
"""

import os
import math
import logging
import shutil
import asyncio
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Optional, List
from utils.s3_utils import read_file_from_s3

from fastapi import APIRouter, Depends, Request, HTTPException, Form, Query, File, UploadFile, Body
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse, FileResponse, StreamingResponse
from fastapi.templating import Jinja2Templates
from auth.dependencies import require_login
from auth.rbac import require_roles
from db_config.pyqs_admin_db import (
    get_levels_by_site_id,
    get_syllabus_by_level,
    get_grades_by_syllabus,
    get_subjects_by_syllabus,
    get_all_exams,
    get_exam_by_id,
    create_exam,
    update_exam,
    get_exam_documents,
    get_exam_document_by_id,
    create_exam_document,
    update_exam_document_content,
    remove_exam_document,
    get_exam_solutions,
    create_exam_solution,
    update_exam_solution,
    delete_exam_solution,
    delete_all_exam_solutions
)
from models.pyqs_admin_models import (
    ExamCreate,
    ExamUpdate,
    ExamDocumentCreate,
    ExamSolutionCreate,
    ExamSolutionUpdate,
    ExtractContentRequest,
    ExtractQuestionsRequest
)
from utils.pyqs_utils import (
    upload_question_paper_to_s3,
    extract_text_from_pdf,
    extract_html_from_pdf,
    extract_questions_from_pdf
)
from utils.pdf_extraction_utils import get_extraction_json_path
import config

logger = logging.getLogger(__name__)

# Initialize the router
router = APIRouter(prefix="/pyqs_admin", tags=["pyqs_admin"])

# Initialize the templates
templates = Jinja2Templates(directory="web/templates")


@router.get("/", response_class=HTMLResponse)
async def pyqs_admin_home(
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Redirect to the exam list page
    """
    if login_check:
        return login_check

    return RedirectResponse(url="/pyqs_admin/exams")


@router.get("/", response_class=RedirectResponse)
async def pyqs_admin_root(
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Redirect to the exams list
    """
    if login_check:
        return login_check

    return RedirectResponse(url="/pyqs_admin/exams")


@router.get("/exams", response_class=HTMLResponse)
async def exam_list(
    request: Request,
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    search: str = Query(None),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Show the list of exams
    """
    if login_check:
        return login_check

    # Calculate offset
    offset = (page - 1) * limit

    # Get exams
    exams, total_count = get_all_exams(limit=limit, offset=offset, search=search)

    # Calculate total pages
    total_pages = math.ceil(total_count / limit)

    return templates.TemplateResponse(
        "pyqs_admin/exam_list.html",
        {
            "request": request,
            "exams": exams,
            "total": total_count,
            "page": page,
            "limit": limit,
            "pages": total_pages,
            "search": search or ""
        }
    )


@router.get("/exams/create", response_class=HTMLResponse)
async def create_exam_form(
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Show the form for creating a new exam
    """
    if login_check:
        return login_check

    # Get levels for initial dropdown
    levels = get_levels_by_site_id()

    return templates.TemplateResponse(
        "pyqs_admin/create_exam.html",
        {
            "request": request,
            "levels": levels,
            "is_edit": False
        }
    )


@router.post("/exams/create", response_class=HTMLResponse)
async def create_exam_submit(
    request: Request,
    exam_name: str = Form(...),
    level: str = Form(...),
    syllabus: str = Form(...),
    grade: str = Form(...),
    subject: str = Form(...),
    syllabus_text: str = Form(""),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Handle the form submission for creating a new exam
    """
    if login_check:
        return login_check

    try:
        # Create exam data
        exam_data = {
            "exam_name": exam_name,
            "level": level,
            "syllabus": syllabus,
            "grade": grade,
            "subject": subject,
            "syllabus_text": syllabus_text
        }

        # Get username from session
        username = request.session.get("user", {}).get("username", "system")

        # Create exam
        create_exam(exam_data, username)

        # Redirect to exam list
        return RedirectResponse(url="/pyqs_admin/exams", status_code=303)
    except ValueError as e:
        # Handle duplicate exam error
        logger.warning(f"Duplicate exam error: {e}")

        # Get levels for dropdown
        levels = get_levels_by_site_id()

        # Get syllabi for the selected level
        syllabi = get_syllabus_by_level(level)

        # Get grades for the selected syllabus
        grades = get_grades_by_syllabus(syllabus)

        # Get subjects for the selected syllabus
        subjects = get_subjects_by_syllabus(syllabus)

        return templates.TemplateResponse(
            "pyqs_admin/create_exam.html",
            {
                "request": request,
                "levels": levels,
                "syllabi": syllabi,
                "grades": grades,
                "subjects": subjects,
                "error": str(e),
                "exam_name": exam_name,
                "level": level,
                "syllabus": syllabus,
                "grade": grade,
                "subject": subject,
                "syllabus_text": syllabus_text,
                "is_edit": False
            },
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error creating exam: {e}")

        # Get levels for dropdown
        levels = get_levels_by_site_id()

        return templates.TemplateResponse(
            "pyqs_admin/create_exam.html",
            {
                "request": request,
                "levels": levels,
                "error": f"Error creating exam: {str(e)}",
                "exam_name": exam_name,
                "level": level,
                "syllabus": syllabus,
                "grade": grade,
                "subject": subject,
                "syllabus_text": syllabus_text,
                "is_edit": False
            },
            status_code=400
        )


@router.get("/exams/edit/{exam_id}", response_class=HTMLResponse)
async def edit_exam_form(
    request: Request,
    exam_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Show the form for editing an exam
    """
    if login_check:
        return login_check

    # Get exam
    exam = get_exam_by_id(exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Get levels for dropdown
    levels = get_levels_by_site_id()

    # Get syllabi for the selected level
    syllabi = get_syllabus_by_level(exam["level"])

    # Get grades for the selected syllabus
    grades = get_grades_by_syllabus(exam["syllabus"])

    # Get subjects for the selected syllabus
    subjects = get_subjects_by_syllabus(exam["syllabus"])

    return templates.TemplateResponse(
        "pyqs_admin/create_exam.html",
        {
            "request": request,
            "exam": exam,
            "levels": levels,
            "syllabi": syllabi,
            "grades": grades,
            "subjects": subjects,
            "is_edit": True
        }
    )


@router.post("/exams/edit/{exam_id}", response_class=HTMLResponse)
async def edit_exam_submit(
    request: Request,
    exam_id: int,
    exam_name: str = Form(...),
    level: str = Form(...),
    syllabus: str = Form(...),
    grade: str = Form(...),
    subject: str = Form(...),
    syllabus_text: str = Form(""),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Handle the form submission for editing an exam
    """
    if login_check:
        return login_check

    try:
        # Create exam data
        exam_data = {
            "exam_name": exam_name,
            "level": level,
            "syllabus": syllabus,
            "grade": grade,
            "subject": subject,
            "syllabus_text": syllabus_text
        }

        # Update exam
        success = update_exam(exam_id, exam_data)
        if not success:
            raise HTTPException(status_code=404, detail="Exam not found")

        # Redirect to exam list
        return RedirectResponse(url="/pyqs_admin/exams", status_code=303)
    except ValueError as e:
        # Handle duplicate exam error
        logger.warning(f"Duplicate exam error: {e}")

        # Get levels for dropdown
        levels = get_levels_by_site_id()

        # Get syllabi for the selected level
        syllabi = get_syllabus_by_level(level)

        # Get grades for the selected syllabus
        grades = get_grades_by_syllabus(syllabus)

        # Get subjects for the selected syllabus
        subjects = get_subjects_by_syllabus(syllabus)

        return templates.TemplateResponse(
            "pyqs_admin/create_exam.html",
            {
                "request": request,
                "levels": levels,
                "syllabi": syllabi,
                "grades": grades,
                "subjects": subjects,
                "error": str(e),
                "exam": {
                    "id": exam_id,
                    "exam_name": exam_name,
                    "level": level,
                    "syllabus": syllabus,
                    "grade": grade,
                    "subject": subject,
                    "syllabus_text": syllabus_text
                },
                "is_edit": True
            },
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error updating exam: {e}")

        # Get levels for dropdown
        levels = get_levels_by_site_id()

        # Get syllabi for the selected level
        syllabi = get_syllabus_by_level(level)

        # Get grades for the selected syllabus
        grades = get_grades_by_syllabus(syllabus)

        # Get subjects for the selected syllabus
        subjects = get_subjects_by_syllabus(syllabus)

        return templates.TemplateResponse(
            "pyqs_admin/create_exam.html",
            {
                "request": request,
                "levels": levels,
                "syllabi": syllabi,
                "grades": grades,
                "subjects": subjects,
                "error": f"Error updating exam: {str(e)}",
                "exam": {
                    "id": exam_id,
                    "exam_name": exam_name,
                    "level": level,
                    "syllabus": syllabus,
                    "grade": grade,
                    "subject": subject,
                    "syllabus_text": syllabus_text
                },
                "is_edit": True
            },
            status_code=400
        )


# API endpoints for cascading dropdowns
@router.get("/api/levels")
async def get_levels(
    request: Request,
    site_id: int = Query(1),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get all levels for a specific site_id
    """
    if login_check:
        return login_check

    levels = get_levels_by_site_id(site_id)
    return {"levels": levels}


@router.get("/api/syllabi/{level}")
async def get_syllabi(
    request: Request,
    level: str,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get all syllabi for a specific level
    """
    if login_check:
        return login_check

    syllabi = get_syllabus_by_level(level)
    return {"syllabi": syllabi}


@router.get("/api/grades/{syllabus}")
async def get_grades(
    request: Request,
    syllabus: str,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get all grades for a specific syllabus
    """
    if login_check:
        return login_check

    grades = get_grades_by_syllabus(syllabus)
    return {"grades": grades}


@router.get("/api/subjects/{syllabus}")
async def get_subjects(
    request: Request,
    syllabus: str,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get all subjects for a specific syllabus
    """
    if login_check:
        return login_check

    subjects = get_subjects_by_syllabus(syllabus)
    return {"subjects": subjects}


# Document management routes
@router.get("/exams/{exam_id}/documents", response_class=HTMLResponse)
async def exam_documents(
    request: Request,
    exam_id: int,
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Show the list of documents for a specific exam
    """
    if login_check:
        return login_check

    # Calculate offset
    offset = (page - 1) * limit

    # Get exam details
    exam = get_exam_by_id(exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Get documents
    documents, total_count = get_exam_documents(exam_id, limit=limit, offset=offset)

    # Calculate total pages
    total_pages = math.ceil(total_count / limit)

    return templates.TemplateResponse(
        "pyqs_admin/exam_documents.html",
        {
            "request": request,
            "exam": exam,
            "documents": documents,
            "total": total_count,
            "page": page,
            "limit": limit,
            "pages": total_pages
        }
    )


@router.post("/exams/{exam_id}/documents", response_class=HTMLResponse)
async def upload_document(
    request: Request,
    exam_id: int,
    year: int = Form(...),
    month: Optional[str] = Form(None),
    shift: Optional[str] = Form(None),
    file: UploadFile = File(...),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Upload a new document for a specific exam
    """
    if login_check:
        return login_check

    # Get exam details
    exam = get_exam_by_id(exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Check file type
    if not file.filename.lower().endswith(".pdf"):
        return templates.TemplateResponse(
            "pyqs_admin/exam_documents.html",
            {
                "request": request,
                "exam": exam,
                "documents": [],
                "total": 0,
                "page": 1,
                "limit": 10,
                "pages": 0,
                "error": "Only PDF files are allowed"
            }
        )

    try:
        # Read file content
        file_content = await file.read()

        # Upload to S3
        success, s3_path = upload_question_paper_to_s3(
            file_content,
            exam_id,
            year,
            month,
            shift
        )

        if not success:
            return templates.TemplateResponse(
                "pyqs_admin/exam_documents.html",
                {
                    "request": request,
                    "exam": exam,
                    "documents": [],
                    "total": 0,
                    "page": 1,
                    "limit": 10,
                    "pages": 0,
                    "error": "Failed to upload file to S3"
                }
            )

        # Create document record in database
        document_data = {
            "year": year,
            "month": month,
            "shift": shift,
            "question_paper_path": s3_path
        }

        # Get username from session
        username = request.session.get("username", "system")

        # Create document
        document_id = create_exam_document(exam_id, document_data, username)

        # Redirect to documents list
        return RedirectResponse(
            url=f"/pyqs_admin/exams/{exam_id}/documents",
            status_code=303  # HTTP 303 See Other
        )

    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        return templates.TemplateResponse(
            "pyqs_admin/exam_documents.html",
            {
                "request": request,
                "exam": exam,
                "documents": [],
                "total": 0,
                "page": 1,
                "limit": 10,
                "pages": 0,
                "error": f"Error uploading document: {str(e)}"
            }
        )


@router.get("/documents/{document_id}/view", response_class=HTMLResponse)
async def view_document(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    View a document
    """
    if login_check:
        return login_check

    # Get document details
    document = get_exam_document_by_id(document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    # Get exam details
    exam = get_exam_by_id(document["exam_id"])
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    return templates.TemplateResponse(
        "pyqs_admin/view_document.html",
        {
            "request": request,
            "document": document,
            "exam": exam
        }
    )


@router.get("/documents/{document_id}/pdf")
async def get_pdf(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get the PDF file for a document
    """
    if login_check:
        return login_check

    # Get document details
    document = get_exam_document_by_id(document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    # Get the full S3 path
    full_s3_path = os.path.join(config.S3_MOUNT_PATH, document["question_paper_path"])

    # Read the file using the S3 utility function that handles sudo permissions
    file_content = read_file_from_s3(full_s3_path)

    if file_content is None:
        raise HTTPException(status_code=404, detail="PDF file not found or could not be read")

    # Create a temporary file to serve
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
    temp_path = temp_file.name

    try:
        # Write the content to the temporary file
        with open(temp_path, "wb") as f:
            f.write(file_content)

        # Return the file with Content-Disposition set to inline to prevent download
        response = FileResponse(
            temp_path,
            media_type="application/pdf",
            filename=os.path.basename(document["question_paper_path"])
        )

        # Set Content-Disposition header to inline to display in browser
        response.headers["Content-Disposition"] = f"inline; filename=\"{os.path.basename(document['question_paper_path'])}\""

        # Set up cleanup callback to delete the temporary file after response is sent
        async def cleanup():
            try:
                os.unlink(temp_path)
            except Exception as e:
                logger.error(f"Error cleaning up temporary file {temp_path}: {e}")

        response.background = cleanup

        return response
    except Exception as e:
        # Clean up the temporary file if an error occurs
        try:
            os.unlink(temp_path)
        except:
            pass

        logger.error(f"Error serving PDF file: {e}")
        raise HTTPException(status_code=500, detail="Error serving PDF file")


@router.post("/documents/{document_id}/extract-content")
async def extract_content(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Extract HTML content from a document
    """
    if login_check:
        return login_check

    # Get document details
    document = get_exam_document_by_id(document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    try:
        # Extract HTML content
        html_content = extract_html_from_pdf(document["question_paper_path"])
        if not html_content:
            return JSONResponse(
                status_code=500,
                content={"error": "Failed to extract HTML content"}
            )

        # Update document with extracted content
        success = update_exam_document_content(document_id, html_content)
        if not success:
            return JSONResponse(
                status_code=500,
                content={"error": "Failed to update document with extracted content"}
            )

        return JSONResponse(
            content={"success": True, "message": "Content extracted successfully"}
        )

    except Exception as e:
        logger.error(f"Error extracting content: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error extracting content: {str(e)}"}
        )


@router.get("/documents/{document_id}/view-content", response_class=HTMLResponse)
async def view_content(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    View the extracted HTML content of a document
    """
    if login_check:
        return login_check

    # Get document details
    document = get_exam_document_by_id(document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    # Check if document has extracted content
    if not document.get("extracted_content"):
        raise HTTPException(status_code=404, detail="Document has no extracted content")

    # Get exam details
    exam = get_exam_by_id(document["exam_id"])
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    return templates.TemplateResponse(
        "pyqs_admin/view_content.html",
        {
            "request": request,
            "document": document,
            "exam": exam
        }
    )


@router.post("/documents/{document_id}/extract-questions")
async def extract_questions(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Extract questions from a document
    """
    if login_check:
        return login_check

    # Get document details
    document = get_exam_document_by_id(document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    try:
        # Extract questions
        questions = extract_questions_from_pdf(document["question_paper_path"])
        if not questions:
            return JSONResponse(
                status_code=500,
                content={"error": "Failed to extract questions"}
            )

        # Get username from session
        username = request.session.get("username", "system")

        # Delete existing solutions for this document
        delete_all_exam_solutions(document_id)

        # Create solutions
        for question in questions:
            solution_data = {
                "exam_dtl_id": document_id,
                **question
            }
            create_exam_solution(solution_data, username)

        # No need to update the document since we're not using flags

        return JSONResponse(
            content={
                "success": True,
                "message": f"Extracted {len(questions)} questions successfully"
            }
        )

    except Exception as e:
        logger.error(f"Error extracting questions: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error extracting questions: {str(e)}"}
        )


@router.get("/documents/{document_id}/edit-questions", response_class=HTMLResponse)
async def edit_questions(
    request: Request,
    document_id: int,
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Edit the extracted questions of a document
    """
    if login_check:
        return login_check

    # Get document details
    document = get_exam_document_by_id(document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    # Get exam details
    exam = get_exam_by_id(document["exam_id"])
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Calculate offset
    offset = (page - 1) * limit

    # Get solutions
    solutions, total_count = get_exam_solutions(document_id, limit=limit, offset=offset)

    # Calculate total pages
    total_pages = math.ceil(total_count / limit)

    return templates.TemplateResponse(
        "pyqs_admin/edit_questions.html",
        {
            "request": request,
            "document": document,
            "exam": exam,
            "solutions": solutions,
            "total": total_count,
            "page": page,
            "limit": limit,
            "pages": total_pages,
            "max": max,  # Add the max function to the template context
            "min": min   # Add the min function to the template context
        }
    )


@router.post("/solutions/{solution_id}")
async def update_solution(
    request: Request,
    solution_id: int,
    solution_data: dict = Body(...),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Update a solution
    """
    if login_check:
        return login_check

    try:
        # Process numeric fields to handle empty values
        if 'marks' in solution_data and (solution_data['marks'] == '' or solution_data['marks'] is None):
            solution_data['marks'] = 0.0
        elif 'marks' in solution_data:
            solution_data['marks'] = float(solution_data['marks'])

        if 'negative_mark' in solution_data and (solution_data['negative_mark'] == '' or solution_data['negative_mark'] is None):
            solution_data['negative_mark'] = 0.0
        elif 'negative_mark' in solution_data:
            solution_data['negative_mark'] = float(solution_data['negative_mark'])

        # Update solution
        success = update_exam_solution(solution_id, solution_data)
        if not success:
            return JSONResponse(
                status_code=500,
                content={"error": "Failed to update solution"}
            )

        return JSONResponse(
            content={"success": True, "message": "Solution updated successfully"}
        )

    except Exception as e:
        logger.error(f"Error updating solution: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error updating solution: {str(e)}"}
        )


@router.delete("/solutions/{solution_id}")
async def delete_solution(
    request: Request,
    solution_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Delete a solution
    """
    if login_check:
        return login_check

    try:
        # Delete solution
        success = delete_exam_solution(solution_id)
        if not success:
            return JSONResponse(
                status_code=500,
                content={"error": "Failed to delete solution"}
            )

        return JSONResponse(
            content={"success": True, "message": "Solution deleted successfully"}
        )

    except Exception as e:
        logger.error(f"Error deleting solution: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error deleting solution: {str(e)}"}
        )


@router.post("/documents/{document_id}/delete")
async def delete_document(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Delete a document and all related files and data
    """
    if login_check:
        return login_check

    try:
        # Get document details
        document = get_exam_document_by_id(document_id)
        if not document:
            return JSONResponse(
                status_code=404,
                content={"error": "Document not found"}
            )

        # Get the PDF path
        pdf_path = document.get("question_paper_path")
        if pdf_path:
            # Get the full S3 path
            full_pdf_path = os.path.join(config.S3_MOUNT_PATH, pdf_path)

            # Get the JSON path for extracted content
            json_path = get_extraction_json_path(pdf_path)
            full_json_path = os.path.join(config.S3_MOUNT_PATH, json_path)

            # Delete the files if they exist
            try:
                if os.path.exists(full_pdf_path):
                    os.remove(full_pdf_path)
                    logger.info(f"Deleted PDF file: {full_pdf_path}")

                if os.path.exists(full_json_path):
                    os.remove(full_json_path)
                    logger.info(f"Deleted JSON file: {full_json_path}")

                # Delete any image files that might have been created
                pdf_dir = os.path.dirname(full_pdf_path)
                pdf_name = os.path.basename(full_pdf_path).split('.')[0]
                image_dir = os.path.join(pdf_dir, f"{pdf_name}_images")
                if os.path.exists(image_dir) and os.path.isdir(image_dir):
                    shutil.rmtree(image_dir)
                    logger.info(f"Deleted image directory: {image_dir}")
            except Exception as e:
                logger.error(f"Error deleting files: {e}")
                # Continue with database deletion even if file deletion fails

        # Delete all solutions for this document
        delete_all_exam_solutions(document_id)

        # Delete the document from the database
        success = remove_exam_document(document_id)
        if not success:
            return JSONResponse(
                status_code=500,
                content={"error": "Failed to delete document from database"}
            )

        return JSONResponse(
            content={"success": True, "message": "Document deleted successfully"}
        )

    except Exception as e:
        logger.error(f"Error deleting document: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error deleting document: {str(e)}"}
        )
