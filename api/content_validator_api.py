"""
API endpoints for the Content Extraction Testing and Validation tool.

This module provides API endpoints for validating extracted content against
original source images.
"""

import os
import json
import logging
import tempfile
from typing import Dict, List, Any, Optional

from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from pydantic import BaseModel, Field

from utils.content_validator import ContentValidator
from utils.extraction_storage import get_extraction_result

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter()

# Initialize validator
validator = ContentValidator()


class ValidationRequest(BaseModel):
    """
    Request model for content validation.
    """
    extracted_content: Dict[str, Any]
    content_to_image_mapping: Optional[Dict[str, int]] = None


# Extraction validation is now integrated directly into the main extraction workflow


class ValidationResponse(BaseModel):
    """
    Response model for content validation.
    """
    content_match: bool
    mismatches: List[Dict[str, Any]]
    item_results: Optional[Dict[str, Any]] = None
    image_results: Optional[List[Dict[str, Any]]] = None

    class Config:
        # Allow extra fields for flexibility
        extra = "allow"


# All validation endpoints are now integrated directly into the main extraction workflow