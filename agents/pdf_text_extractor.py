"""
PDF Text Extractor

This module handles the extraction of text from PDF resources.
It converts PDFs to images, extracts text from each image, and saves the result.
"""

import logging
import os
import asyncio
import shutil
import traceback
from typing import Dict

import config
from agents.utils.pdf_helpers import PDFImageConverter
from agents.core.extractor import ExtractorAgent
from utils.s3_utils import upload_file_to_s3
from utils.db_utils import update_extract_path
from sqlalchemy import text
from db_config.db import get_session, CONTENT_SCHEMA
from llm_manager.prompts import pdf_ocr_prompt
# Get logger instance
logger = logging.getLogger(__name__)

# Define storage directory
EXTRACTED_TEXT_DIR = config.PDF_PAGE_IMG_OUTPUT_DIR+"/extracted_text"
os.makedirs(EXTRACTED_TEXT_DIR, exist_ok=True)

# Define the PDF OCR prompt
PDF_OCR_PROMPT = pdf_ocr_prompt


class PDFTextExtractor:
    """
    Manages the extraction of text from PDF resources.
    """

    def __init__(self, max_concurrent_extractions: int = 3, image_delay_seconds: int = 1):
        """
        Initialize the PDFTextExtractor.

        Args:
            max_concurrent_extractions: Maximum number of concurrent extractions to run in parallel threads (default: 5)
            progress_tracker: Optional progress tracker instance
            task_id: Optional task ID for progress tracking
        """
        self.max_concurrent_extractions = max_concurrent_extractions
        self.max_concurrent_extractions = max_concurrent_extractions
        self.image_delay_seconds = image_delay_seconds
        logger.info(f"Initialized PDFTextExtractor with {max_concurrent_extractions} concurrent workers")

    async def process_pdf_text_extraction(self, res_id: str) -> Dict:
        """
        Process the PDF text extraction.

        Args:
            res_id: Resource ID to extract text from

        Returns:
            Dict: Result of the extraction process
        """
        try:
            # Log the extraction request
            logger.info(f"Starting PDF text extraction process for resource ID: {res_id}")

            db_session = next(get_session(CONTENT_SCHEMA))
            try:
                resource_query = text("""
                    SELECT id, chapter_id, res_link, resource_name
                    FROM wscontent.resource_dtl
                    WHERE id = :res_id
                    LIMIT 1
                """)

                resource_result = db_session.execute(resource_query, {"res_id": res_id})
                resource_row = resource_result.fetchone()

                if not resource_row:
                    logger.warning(f"No resource found with ID: {res_id}")
                    return {"status": "error", "message": f"Resource with ID {res_id} not found"}

                # Extract resource details
                resource_id = resource_row[0]
                chapter_id = resource_row[1]
                file_path = resource_row[2]
                resource_name = resource_row[3]
                chapter_query = text("""
                    SELECT book_id
                    FROM wscontent.chapters_mst
                    WHERE id = :chapter_id
                    LIMIT 1
                """)

                chapter_result = db_session.execute(chapter_query, {"chapter_id": chapter_id})
                chapter_row = chapter_result.fetchone()

                if not chapter_row:
                    logger.warning(f"No chapter found with ID: {chapter_id}")
                    return {"status": "error", "message": f"Chapter with ID {chapter_id} not found"}

                # Extract book_id
                book_id = chapter_row[0]
                logger.info(f"Book ID: {book_id}")
            finally:
                # Close the database session
                db_session.close()
                logger.debug("Database session closed after resource and chapter queries")

            pdf_converter = PDFImageConverter()

            # Log that we're about to start the conversion
            logger.info(f"Starting PDF conversion for resource ID: {resource_id}")

            # Run the conversion
            conversion_result = pdf_converter.convert_and_upload(file_path, book_id, chapter_id, resource_id)

            if conversion_result["status"] != "success":
                logger.error(f"PDF conversion failed: {conversion_result['message']}")
                return {"status": "error", "message": conversion_result["message"]}

            # Get the image paths
            image_paths = conversion_result["image_urls"]

            # Define a function to sort by page and column
            def sort_by_page_and_column(url):
                # Extract the filename from the path
                filename = url.split('/')[-1]
                # Remove extension
                name_without_ext = filename.split('.')[0]
                # Split by underscore to get parts
                parts = name_without_ext.split('_')
                # Extract page number (should be at index 1 in 'page_X_col_Y')
                page_num = int(parts[1])
                # Return tuple for sorting (page first, then column)
                return page_num

            # Sort using the custom function that sorts by page first, then by column
            sorted_image_paths = sorted(image_paths, key=sort_by_page_and_column)
            logger.info(sorted_image_paths)

            extractor = ExtractorAgent()

            output_dir = os.path.join(EXTRACTED_TEXT_DIR, str(book_id), str(chapter_id), str(resource_id))
            os.makedirs(output_dir, exist_ok=True)
            final_output_file = os.path.join(output_dir, f"{resource_id}.txt")

            total_images = len(sorted_image_paths)

            # Create a thread pool for parallel processing
            from concurrent.futures import ThreadPoolExecutor

            # Define a function to process a single image (non-async for ThreadPoolExecutor)
            def process_single_image(args):
                i, image_path = args

                try:
                    logger.info(f"Starting processing image {i+1}/{total_images} for resource ID: {resource_id}")
                    # Create a new extractor instance for thread safety
                    thread_extractor = ExtractorAgent()

                    # Run the extraction in the current thread (blocking operation)
                    # We use asyncio.run to execute the async method in a synchronous context
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        result, _ = loop.run_until_complete(thread_extractor._extract_with_prompt([image_path], PDF_OCR_PROMPT, md_parse=False, md_result=False))
                    finally:
                        loop.close()

                    # Save each page's result to a separate file
                    page_file = os.path.join(output_dir, f"{resource_id}_page_{i+1}.txt")
                    with open(page_file, "w", encoding="utf-8") as f:
                        f.write(result)

                    # Log progress
                    logger.info(f"Completed processing image {i+1}/{total_images} for resource ID: {resource_id}")
                    return (i+1, page_file)  # Return tuple with index to ensure correct ordering
                except Exception as e:
                    logger.error(f"Error processing image {i+1}/{total_images}: {e}")
                    logger.error(traceback.format_exc())
                    # Return a tuple with index and None to maintain order but indicate failure
                    return (i+1, None)

            # Use ThreadPoolExecutor to run tasks in parallel
            logger.info(f"Starting parallel processing of {total_images} images with max concurrency of {self.max_concurrent_extractions}")
            import time
            start_time = time.time()

            # Create a list of arguments for each task
            task_args = [(i, image_path) for i, image_path in enumerate(sorted_image_paths)]

            # Execute tasks in parallel using ThreadPoolExecutor
            with ThreadPoolExecutor(max_workers=self.max_concurrent_extractions) as executor:
                # Map the function to the arguments
                results = list(executor.map(process_single_image, task_args))

            end_time = time.time()
            processing_time = end_time - start_time
            logger.info(f"Parallel processing completed in {processing_time:.2f} seconds")

            # Sort results by index to maintain page order
            results.sort(key=lambda x: x[0])  # Sort by the index (first element of tuple)

            # Extract just the page_file paths, filtering out None values (failed extractions)
            page_files = [result[1] for result in results if result[1] is not None]

            # Log how many pages were successfully processed
            logger.info(f"Successfully processed {len(page_files)}/{total_images} images")

            logger.info(f"Completed parallel processing of all {total_images} images")

            # Page files are already in the correct order from our parallel processing
            # No need for additional sorting
            sorted_page_files = page_files

            # Combine all page files into the final output file in sequential order
            with open(final_output_file, "w", encoding="utf-8") as final_file:
                for page_file in sorted_page_files:
                    with open(page_file, "r", encoding="utf-8") as f:
                        final_file.write(f.read() + "\n\n")

            # Delete individual page files after combining
            for page_file in sorted_page_files:
                try:
                    os.remove(page_file)
                    logger.info(f"Deleted temporary file: {page_file}")
                except Exception as e:
                    logger.warning(f"Failed to delete temporary file {page_file}: {e}")

            s3_path = upload_file_to_s3(
                local_file_path=final_output_file,
                book_id=book_id,
                chapter_id=chapter_id,
                res_id=resource_id,
                file_name=f"{resource_id}.txt",
                is_quiz_image=False
            )

            if not s3_path:
                logger.error("Failed to upload file to S3")
                return {"status": "error", "message": "Failed to upload file to S3"}

            # Log the query parameters for debugging
            logger.info(f"Updating database with extract_path: {s3_path} for resource ID: {res_id}")

            # Use the utility function to update the database
            success = update_extract_path(res_id, s3_path, CONTENT_SCHEMA)

            # Delete the resource folder after successful upload to S3
            logger.info(f"Attempting to delete resource folder for book_id: {book_id}, chapter_id: {chapter_id}, res_id: {resource_id}")
            if self.delete_folder_by_id_extracts(book_id, chapter_id, resource_id):
                logger.info(f"Successfully deleted resource folder for res_id: {resource_id}")
            else:
                logger.warning(f"Failed to delete resource folder for res_id: {resource_id}")

            if self.delete_folder_by_id(book_id, chapter_id, res_id):
                logger.info(f"Successfully deleted resource folder for res_id: {res_id}")
            else:
                logger.warning(f"Failed to delete resource folder for res_id: {res_id}")

            return {
                "status": "success",
                "message": "PDF text extraction completed successfully",
                "extract_path": s3_path
            }

        except Exception as e:
            logger.error(f"Error during PDF text extraction: {e}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    async def get_chapters_by_book_id(self, book_id: str) -> Dict:
        """
        Get all chapters for a given book ID.

        Args:
            book_id: Book ID to get chapters for

        Returns:
            Dict: List of chapters with their details
        """
        try:
            # Get a database session for the wscontent schema
            db_session = next(get_session(CONTENT_SCHEMA))
            try:
                # Query the chapters_mst table to get all chapters for the book
                chapters_query = text("""
                    SELECT id, name, book_id
                    FROM wscontent.chapters_mst
                    WHERE book_id = :book_id
                    ORDER BY id
                """)

                chapters_result = db_session.execute(chapters_query, {"book_id": book_id})
                chapters = chapters_result.fetchall()

                if not chapters:
                    logger.warning(f"No chapters found for book ID: {book_id}")
                    return {"status": "error", "message": f"No chapters found for book with ID {book_id}"}

                # Format the chapters as a list of dictionaries
                chapters_list = []
                for chapter in chapters:
                    chapters_list.append({
                        "id": chapter[0],
                        "name": chapter[1],  # Column name is 'name' in the database
                        "book_id": chapter[2]
                    })

                return {
                    "status": "success",
                    "chapters": chapters_list
                }
            finally:
                # Close the database session
                db_session.close()
                logger.debug("Database session closed after getting chapters")

        except Exception as e:
            logger.error(f"Error getting chapters for book ID {book_id}: {e}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    async def process_pdf_text_extraction_by_chapter(self, chapter_id: str) -> Dict:
        """
        Process the PDF text extraction for a specific chapter.

        Args:
            chapter_id: Chapter ID to extract text from

        Returns:
            Dict: Result of the extraction process
        """
        try:
            # Log the extraction request
            logger.info(f"Starting PDF text extraction process for chapter ID: {chapter_id}")

            db_session = next(get_session(CONTENT_SCHEMA))
            try:
                # Get the book_id for the chapter
                chapter_query = text("""
                    SELECT id, name, book_id
                    FROM wscontent.chapters_mst
                    WHERE id = :chapter_id
                    LIMIT 1
                """)

                chapter_result = db_session.execute(chapter_query, {"chapter_id": chapter_id})
                chapter_row = chapter_result.fetchone()

                if not chapter_row:
                    logger.warning(f"No chapter found with ID: {chapter_id}")
                    return {"status": "error", "message": f"Chapter with ID {chapter_id} not found"}

                # Extract chapter details
                chapter_id = chapter_row[0]
                chapter_name = chapter_row[1]
                book_id = chapter_row[2]

                # Find PDF resources for this chapter
                resource_query = text("""
                    SELECT id, res_link, resource_name, extract_path
                    FROM wscontent.resource_dtl
                    WHERE chapter_id = :chapter_id
                    AND res_link LIKE '%.pdf'
                    AND res_type = 'Notes'
                    LIMIT 1
                """)

                resource_result = db_session.execute(resource_query, {"chapter_id": chapter_id})
                resource_row = resource_result.fetchone()

                if not resource_row:
                    logger.warning(f"No PDF resource found for chapter ID: {chapter_id}")
                    return {"status": "error", "message": f"No PDF resource found for chapter with ID {chapter_id}"}

                # Extract resource details
                resource_id = resource_row[0]
                file_path = resource_row[1]
                resource_name = resource_row[2]
                extract_path = resource_row[3]

                # First check if the text file already exists in S3
                from utils.s3_utils import read_file_from_s3, get_s3_path

                # Check if extract_path is already set in the database
                if extract_path:
                    logger.info(f"Extract path already exists in database: {extract_path}")
                    # Try to read the file from S3
                    direct_s3_path = get_s3_path(extract_path)
                    logger.info(f"Checking if file exists at S3 path: {direct_s3_path}")
                    content = read_file_from_s3(direct_s3_path)

                    if content is not None:
                        logger.info(f"Text file already exists in S3, skipping extraction")
                        db_session.close()
                        logger.debug("Database session closed after finding existing extract path")
                        return {
                            "status": "success",
                            "message": "Text file already exists in S3",
                            "chapter_id": chapter_id,
                            "chapter_name": chapter_name,
                            "resource_id": resource_id,
                            "extract_path": extract_path
                        }

                # If extract_path is not set or file doesn't exist, try the expected path
                expected_path = f"supload/pdfextracts/{book_id}/{chapter_id}/{resource_id}/extractedImages/{resource_id}.txt"
                full_expected_path = get_s3_path(expected_path)
                logger.info(f"Checking if file exists at expected S3 path: {full_expected_path}")
                content = read_file_from_s3(full_expected_path)

                if content is not None:
                    logger.info(f"Text file exists at expected path in S3, updating database")
                    # Update the database with the correct path
                    from utils.db_utils import update_extract_path
                    success = update_extract_path(resource_id, expected_path, CONTENT_SCHEMA)

                    if success:
                        logger.info(f"Updated database with correct extract_path: {expected_path}")
                    else:
                        logger.warning(f"Failed to update database with correct path: {expected_path}")

                    db_session.close()
                    logger.debug("Database session closed after finding existing extract path at expected location")
                    return {
                        "status": "success",
                        "message": "Text file already exists in S3",
                        "chapter_id": chapter_id,
                        "chapter_name": chapter_name,
                        "resource_id": resource_id,
                        "extract_path": expected_path
                    }

                # If we get here, the file doesn't exist and we need to extract it
                logger.info(f"Text file not found in S3, proceeding with extraction")
                # Close the database session before calling another method that will create its own session
                db_session.close()
                logger.debug("Database session closed before calling process_pdf_text_extraction")
                # Now proceed with the extraction using the existing method
                # Pass the max_concurrent_extractions parameter from this instance
                result = await self.process_pdf_text_extraction(resource_id)

                # Add chapter information to the result
                if result["status"] == "success":
                    result["chapter_id"] = chapter_id
                    result["chapter_name"] = chapter_name
                    result["resource_id"] = resource_id

                    # Delete the book's folder after successful extraction
                    # We don't need to do this here since process_pdf_text_extraction already does it
                    # But we'll log that we're relying on that function for cleanup
                    logger.info(f"Book folder for book_id: {book_id} should be deleted by process_pdf_text_extraction")

                return result
            finally:
                # Ensure the database session is closed if we haven't closed it already
                if not db_session.is_active:
                    db_session.close()
                    logger.debug("Database session closed at the end of process_pdf_text_extraction_by_chapter")

        except Exception as e:
            logger.error(f"Error during PDF text extraction for chapter ID {chapter_id}: {e}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    async def read_extracted_text(self, res_id: str) -> Dict:
        """
        Read the extracted text for a resource.

        Args:
            res_id: Resource ID to read text from

        Returns:
            Dict: The extracted text content
        """
        try:
            # Get a database session for the wscontent schema
            db_session = next(get_session(CONTENT_SCHEMA))
            try:
                # Query the resource_dtl table to get resource details
                resource_query = text("""
                    SELECT id, chapter_id, extract_path
                    FROM wscontent.resource_dtl
                    WHERE id = :res_id
                    LIMIT 1
                """)

                resource_result = db_session.execute(resource_query, {"res_id": res_id})
                resource_row = resource_result.fetchone()

                if not resource_row:
                    logger.error(f"Resource with ID {res_id} not found in database")
                    return {"status": "error", "message": f"Resource with ID {res_id} not found"}

                # Extract resource details
                resource_id = resource_row[0]
                chapter_id = resource_row[1]
                extract_path = resource_row[2]

                logger.info(f"Found resource ID: {resource_id}, chapter ID: {chapter_id}, extract path: {extract_path}")

                if not extract_path:
                    logger.error(f"No extract_path found in database for resource ID {res_id}")
                    return {"status": "error", "message": f"No extracted text found for resource with ID {res_id}"}

                # Query the chapters_mst table to get the book_id
                chapter_query = text("""
                    SELECT book_id
                    FROM wscontent.chapters_mst
                    WHERE id = :chapter_id
                    LIMIT 1
                """)

                chapter_result = db_session.execute(chapter_query, {"chapter_id": chapter_id})
                chapter_row = chapter_result.fetchone()

                if not chapter_row:
                    logger.error(f"Chapter with ID {chapter_id} not found in database")
                    return {"status": "error", "message": f"Chapter with ID {chapter_id} not found"}

                # Extract book_id
                book_id = chapter_row[0]
                logger.info(f"Found book ID: {book_id}")

                # First try to read directly from the extract_path
                # This is the path stored in the database
                try:
                    from utils.s3_utils import read_file_from_s3, get_s3_path

                    # Try direct path first
                    direct_s3_path = get_s3_path(extract_path)
                    logger.info(f"Trying to read from direct S3 path: {direct_s3_path}")
                    content = read_file_from_s3(direct_s3_path)

                    if content is not None:
                        # Convert bytes to string
                        content_str = content.decode("utf-8")
                        logger.info(f"Successfully read {len(content_str)} characters from direct S3 path")
                        db_session.close()
                        logger.debug("Database session closed after finding content at direct S3 path")
                        return {"status": "success", "content": content_str, "source": "s3_direct"}
                    else:
                        logger.warning(f"Could not read from direct S3 path: {direct_s3_path}")
                except Exception as e:
                    logger.warning(f"Error reading from direct S3 path: {e}")

                # Check if the file exists locally
                local_file_path = os.path.join(EXTRACTED_TEXT_DIR, str(book_id), str(chapter_id), str(resource_id), f"{resource_id}.txt")
                logger.info(f"Checking local file path: {local_file_path}")

                if os.path.exists(local_file_path):
                    # Read the file content
                    with open(local_file_path, "r", encoding="utf-8") as f:
                        content = f.read()
                    logger.info(f"Successfully read {len(content)} characters from local file")
                    db_session.close()
                    logger.debug("Database session closed after finding content in local file")
                    return {"status": "success", "content": content, "source": "local"}
                else:
                    logger.warning(f"Local file not found: {local_file_path}")

                # Try to construct the S3 path based on the expected structure
                # This is a fallback in case the extract_path in the database is incorrect
                try:
                    constructed_s3_path = f"supload/pdfextracts/{book_id}/{chapter_id}/{resource_id}/extractedImages/{resource_id}.txt"
                    full_s3_path = get_s3_path(constructed_s3_path)
                    logger.info(f"Trying constructed S3 path: {full_s3_path}")

                    content = read_file_from_s3(full_s3_path)
                    if content is not None:
                        # Convert bytes to string
                        content_str = content.decode("utf-8")
                        logger.info(f"Successfully read {len(content_str)} characters from constructed S3 path")

                        # Update the database with the correct path
                        try:
                            # Use the utility function to update the database
                            success = update_extract_path(res_id, constructed_s3_path, CONTENT_SCHEMA)

                            if success:
                                logger.info(f"Updated database with correct extract_path: {constructed_s3_path}")
                            else:
                                logger.warning(f"Failed to update database with correct path: {constructed_s3_path}")
                        except Exception as update_err:
                            logger.error(f"Failed to update database with correct path: {update_err}")
                            logger.error(traceback.format_exc())

                        db_session.close()
                        logger.debug("Database session closed after finding content at constructed S3 path")
                        return {"status": "success", "content": content_str, "source": "s3_constructed"}
                    else:
                        logger.warning(f"Could not read from constructed S3 path: {full_s3_path}")
                except Exception as e:
                    logger.warning(f"Error reading from constructed S3 path: {e}")

                # If we've reached here, we couldn't find the file
                logger.error(f"Extracted text file not found for resource ID {res_id}")
                return {"status": "error", "message": f"Extracted text file not found for resource with ID {res_id}"}
            finally:
                # Close the database session if it's still active
                if hasattr(db_session, 'is_active') and db_session.is_active:
                    db_session.close()
                    logger.debug("Database session closed at the end of read_extracted_text")

        except Exception as e:
            logger.error(f"Error reading extracted text: {e}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    def delete_folder_by_id_extracts(self, book_id, chapter_id=None, res_id=None):
        """
        Delete a folder by ID from the extracted_text directory

        Parameters:
        book_id (str): The book ID
        chapter_id (str, optional): The chapter ID. If provided, deletes extracted_text/book_id/chapter_id path
        res_id (str, optional): The resource ID. If provided, deletes extracted_text/book_id/chapter_id/res_id path

        Returns:
        bool: True if deletion was successful, False otherwise
        """
        try:
            base_path = Path(config.PDF_PAGE_IMG_OUTPUT_DIR) / 'extracted_text'

            # Determine the path to delete based on provided parameters
            if res_id is not None and chapter_id is not None:
                # Delete specific resource folder: extracted_text/book_id/chapter_id/res_id
                folder_path = base_path / str(book_id) / str(chapter_id) / str(res_id)
                logger.info(f"Deleting extracted text resource folder: {folder_path}")
            elif chapter_id is not None:
                # Delete chapter folder: extracted_text/book_id/chapter_id
                folder_path = base_path / str(book_id) / str(chapter_id)
                logger.info(f"Deleting extracted text chapter folder: {folder_path}")
            else:
                # Delete book folder: extracted_text/book_id
                folder_path = base_path / str(book_id)
                logger.info(f"Deleting extracted text book folder: {folder_path}")

            if not folder_path.exists():
                logger.info(f"Folder {folder_path} does not exist")
                return False

            shutil.rmtree(folder_path)
            logger.info(f"Successfully deleted folder: {folder_path}")
            return True
        except Exception as e:
            logger.info(f"Error deleting extracted text folder: {str(e)}")
            return False

    def delete_folder_by_id(self, book_id, chapter_id=None, res_id=None):
        """
        Delete a folder by ID from the local_page_images directory

        Parameters:
        book_id (str): The book ID
        chapter_id (str, optional): The chapter ID. If provided, deletes book_id/chapter_id path
        res_id (str, optional): The resource ID. If provided, deletes book_id/chapter_id/res_id path

        Returns:
        bool: True if deletion was successful, False otherwise
        """
        try:
            base_path = Path(config.PDF_PAGE_IMG_OUTPUT_DIR)

            # Determine the path to delete based on provided parameters
            if res_id is not None and chapter_id is not None:
                # Delete specific resource folder: book_id/chapter_id/res_id
                folder_path = base_path / str(book_id) / str(chapter_id) / str(res_id)
                logger.info(f"Deleting resource folder: {folder_path}")
            elif chapter_id is not None:
                # Delete chapter folder: book_id/chapter_id
                folder_path = base_path / str(book_id) / str(chapter_id)
                logger.info(f"Deleting chapter folder: {folder_path}")
            else:
                # Delete book folder: book_id
                folder_path = base_path / str(book_id)
                logger.info(f"Deleting book folder: {folder_path}")

            if not folder_path.exists():
                logger.info(f"Folder {folder_path} does not exist")
                return False

            shutil.rmtree(folder_path)
            logger.info(f"Successfully deleted folder: {folder_path}")
            return True
        except Exception as e:
            logger.info(f"Error deleting folder: {str(e)}")
            return False