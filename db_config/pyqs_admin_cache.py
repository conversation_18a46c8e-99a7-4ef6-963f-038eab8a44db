"""
Redis cache utility for the PYQs Admin module.
"""

import json
import logging
from utils.redis_util import get_redis_client
from config import EXPIRATION_TIME

logger = logging.getLogger(__name__)

# Connect to Redis server
r = get_redis_client()

# Cache expiration times (in seconds)
DROPDOWN_CACHE_EXPIRY = 600  # 10 mins
EXAM_CACHE_EXPIRY = 600  # 10 mins

# Cache key prefixes
EXAMS_LIST_PREFIX = "pyqs_admin:exams:all"
EXAM_DETAIL_PREFIX = "pyqs_admin:exam"
LEVELS_KEY = "pyqs_admin:levels"
SYLLABUS_PREFIX = "pyqs_admin:syllabus"
GRADES_PREFIX = "pyqs_admin:grades"
SUBJECTS_PREFIX = "pyqs_admin:subjects"


def get_exams_list_cache_key(limit=10, offset=0, search=None):
    """
    Generate a cache key for the exams list.
    
    Args:
        limit (int): Number of exams per page
        offset (int): Offset for pagination
        search (str): Search term
        
    Returns:
        str: Cache key
    """
    page = (offset // limit) + 1 if limit > 0 else 1
    search_part = f":{search}" if search else ""
    return f"{EXAMS_LIST_PREFIX}:{page}:{limit}{search_part}"


def get_exam_detail_cache_key(exam_id):
    """
    Generate a cache key for an exam detail.
    
    Args:
        exam_id (int): The ID of the exam
        
    Returns:
        str: Cache key
    """
    return f"{EXAM_DETAIL_PREFIX}:{exam_id}"


def get_syllabus_cache_key(level):
    """
    Generate a cache key for syllabi by level.
    
    Args:
        level (str): The level name
        
    Returns:
        str: Cache key
    """
    return f"{SYLLABUS_PREFIX}:{level}"


def get_grades_cache_key(syllabus):
    """
    Generate a cache key for grades by syllabus.
    
    Args:
        syllabus (str): The syllabus name
        
    Returns:
        str: Cache key
    """
    return f"{GRADES_PREFIX}:{syllabus}"


def get_subjects_cache_key(syllabus):
    """
    Generate a cache key for subjects by syllabus.
    
    Args:
        syllabus (str): The syllabus name
        
    Returns:
        str: Cache key
    """
    return f"{SUBJECTS_PREFIX}:{syllabus}"


def get_from_cache(key):
    """
    Get data from Redis cache.
    
    Args:
        key (str): The cache key
        
    Returns:
        dict/list/None: The cached data or None if not found
    """
    try:
        cached_data = r.get(key)
        if cached_data:
            logger.info(f"Cache hit for key: {key}")
            return None
        logger.info(f"Cache miss for key: {key}")
        return None
    except Exception as e:
        logger.error(f"Error getting data from cache: {e}")
        return None


def set_in_cache(key, data, expiry=EXAM_CACHE_EXPIRY):
    """
    Set data in Redis cache.
    
    Args:
        key (str): The cache key
        data (dict/list): The data to cache
        expiry (int): Expiration time in seconds
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        r.set(key, json.dumps(data), ex=expiry)
        logger.info(f"Data cached with key: {key}, expiry: {expiry}s")
        return True
    except Exception as e:
        logger.error(f"Error setting data in cache: {e}")
        return False


def invalidate_cache(key_pattern):
    """
    Invalidate cache keys matching a pattern.
    
    Args:
        key_pattern (str): The pattern to match keys
        
    Returns:
        int: Number of keys invalidated
    """
    try:
        keys = r.keys(key_pattern)
        if keys:
            count = r.delete(*keys)
            logger.info(f"Invalidated {count} cache keys matching pattern: {key_pattern}")
            return count
        return 0
    except Exception as e:
        logger.error(f"Error invalidating cache: {e}")
        return 0


def invalidate_exams_list_cache():
    """
    Invalidate all exam list cache entries.
    
    Returns:
        int: Number of keys invalidated
    """
    return invalidate_cache(f"{EXAMS_LIST_PREFIX}:*")


def invalidate_exam_detail_cache(exam_id):
    """
    Invalidate the cache for a specific exam.
    
    Args:
        exam_id (int): The ID of the exam
        
    Returns:
        int: Number of keys invalidated (0 or 1)
    """
    key = get_exam_detail_cache_key(exam_id)
    try:
        if r.exists(key):
            r.delete(key)
            logger.info(f"Invalidated cache for exam ID: {exam_id}")
            return 1
        return 0
    except Exception as e:
        logger.error(f"Error invalidating exam cache: {e}")
        return 0
