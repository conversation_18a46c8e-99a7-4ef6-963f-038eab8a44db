from db_config.db import get_engine, get_session, CONTENT_SCHEMA
import logging
from contextlib import contextmanager

logger = logging.getLogger(__name__)

@contextmanager
def get_connection():
    """
    Context manager to get a connection from the engine.
    This ensures the connection is properly closed after use.
    """
    # Get the engine
    engine = get_engine(CONTENT_SCHEMA)

    # Create a new connection
    connection = engine.raw_connection()
    try:
        yield connection.cursor()
        connection.commit()
    except Exception as e:
        connection.rollback()
        raise e
    finally:
        connection.close()

def check_table_structure():
    """
    Check if the gptprompts table exists and log its structure
    """
    try:
        # Use our context manager to get a connection
        with get_connection() as cursor:
            # Check if the table exists
            cursor.execute("""
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = %s
                AND table_name = 'gptprompts'
            """, (CONTENT_SCHEMA,))
            table_exists = cursor.fetchone()[0] > 0

            if not table_exists:
                logger.error(f"Table 'gptprompts' does not exist in schema '{CONTENT_SCHEMA}'")
                return False

            # Get the columns of the table
            cursor.execute("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_schema = %s
                AND table_name = 'gptprompts'
            """, (CONTENT_SCHEMA,))
            columns = cursor.fetchall()

            logger.info(f"Table 'gptprompts' has the following columns:")
            for column in columns:
                logger.info(f"  {column[0]}: {column[1]} (nullable: {column[2]})")

            # Get the primary key of the table
            cursor.execute("""
                SELECT column_name
                FROM information_schema.key_column_usage
                WHERE table_schema = %s
                AND table_name = 'gptprompts'
                AND constraint_name = 'PRIMARY'
            """, (CONTENT_SCHEMA,))
            pk_columns = [row[0] for row in cursor.fetchall()]
            logger.info(f"Primary key: {pk_columns}")

            # Count the number of records in the table
            cursor.execute(f"SELECT COUNT(*) FROM {CONTENT_SCHEMA}.gptprompts")
            count = cursor.fetchone()[0]
            logger.info(f"Number of records in the table: {count}")

            return True
    except Exception as e:
        logger.error(f"Error checking table structure: {e}")
        return False

def get_all_prompts(limit=100, offset=0):
    """
    Get all prompts from the gptprompts table in descending order

    Args:
        limit (int): Maximum number of records to return
        offset (int): Offset for pagination

    Returns:
        list: List of prompts
    """
    try:
        # Create the SQL query
        query = f"""
            SELECT id, prompt, prompt_category, prompt_label, prompt_description, version
            FROM {CONTENT_SCHEMA}.gptprompts
            ORDER BY id DESC
            LIMIT %s OFFSET %s
        """

        # Use our context manager to get a connection
        with get_connection() as cursor:
            # Execute the query
            cursor.execute(query, (limit, offset))

            # Fetch all results
            rows = cursor.fetchall()

            # Get column names
            columns = [col[0] for col in cursor.description]

            # Convert to list of dictionaries
            prompts = [dict(zip(columns, row)) for row in rows]

            return prompts
    except Exception as e:
        logger.error(f"Error getting prompts: {e}")
        raise


def get_prompt_by_id(prompt_id):
    """
    Get a prompt by its ID

    Args:
        prompt_id (int): The ID of the prompt

    Returns:
        dict: The prompt data
    """
    try:
        # Create the SQL query
        query = f"""
            SELECT id, prompt, prompt_category, prompt_label, prompt_description, version
            FROM {CONTENT_SCHEMA}.gptprompts
            WHERE id = %s
        """

        # Use our context manager to get a connection
        with get_connection() as cursor:
            # Execute the query
            cursor.execute(query, (prompt_id,))

            # Fetch the first row
            row = cursor.fetchone()

            if row:
                # Get column names
                columns = [col[0] for col in cursor.description]

                # Convert to dictionary
                return dict(zip(columns, row))
            return None
    except Exception as e:
        logger.error(f"Error getting prompt by ID: {e}")
        raise


def create_prompt(prompt_data):
    """
    Create a new prompt

    Args:
        prompt_data (dict): The prompt data

    Returns:
        int: The ID of the newly created prompt
    """
    try:
        # Log the prompt data for debugging
        logger.info(f"Creating prompt with data: {prompt_data}")

        # Create the SQL query
        query = f"""
            INSERT INTO {CONTENT_SCHEMA}.gptprompts
            (prompt, prompt_category, prompt_label, prompt_description, version)
            VALUES
            (%s, %s, %s, %s, 0)
        """

        # Prepare parameters
        params = (
            prompt_data["prompt"],
            prompt_data["promptCategory"],
            prompt_data["promptLabel"],
            prompt_data.get("promptDescription", "")
        )

        # Log the SQL query and parameters
        logger.info(f"SQL Query: {query}")
        logger.info(f"Parameters: {params}")

        # Use our context manager to get a connection
        with get_connection() as cursor:
            # Execute the query
            cursor.execute(query, params)
            logger.info("Insert query executed successfully")

            # Get the ID of the newly created prompt
            cursor.execute("SELECT LAST_INSERT_ID() as id")
            last_id = cursor.fetchone()[0]

            logger.info(f"Created prompt with ID: {last_id}")
            return last_id
    except Exception as e:
        logger.error(f"Error creating prompt: {e}")
        raise


def update_prompt(prompt_id, prompt_data):
    """
    Update an existing prompt

    Args:
        prompt_id (int): The ID of the prompt to update
        prompt_data (dict): The updated prompt data

    Returns:
        bool: True if the update was successful, False otherwise
    """
    try:
        # Log the prompt data for debugging
        logger.info(f"Updating prompt {prompt_id} with data: {prompt_data}")

        # Create the SQL query
        query = f"""
            UPDATE {CONTENT_SCHEMA}.gptprompts
            SET prompt = %s,
                prompt_category = %s,
                prompt_label = %s,
                prompt_description = %s,
                version = version + 1
            WHERE id = %s
        """

        # Prepare parameters
        params = (
            prompt_data["prompt"],
            prompt_data["promptCategory"],
            prompt_data["promptLabel"],
            prompt_data.get("promptDescription", ""),
            prompt_id
        )

        # Log the SQL query and parameters
        logger.info(f"SQL Query: {query}")
        logger.info(f"Parameters: {params}")

        # Use our context manager to get a connection
        with get_connection() as cursor:
            # Execute the query
            cursor.execute(query, params)
            logger.info("Update query executed successfully")
            rows_affected = cursor.rowcount

            logger.info(f"Updated prompt {prompt_id}, rows affected: {rows_affected}")
            return rows_affected > 0
    except Exception as e:
        logger.error(f"Error updating prompt: {e}")
        raise


def get_prompt_by_category_and_label(prompt_category, prompt_label):
    """
    Get a prompt by its category and label

    Args:
        prompt_category (str): The category of the prompt (e.g., 'ibookgpt', 'gptsir')
        prompt_label (str): The label of the prompt

    Returns:
        str: The prompt text if found, None otherwise
    """
    try:
        # Log the request
        logger.info(f"Getting prompt for category: {prompt_category}, label: {prompt_label}")

        # Create the SQL query
        query = f"""
            SELECT prompt
            FROM {CONTENT_SCHEMA}.gptprompts
            WHERE prompt_category = %s AND prompt_label = %s
            ORDER BY version DESC
            LIMIT 1
        """

        # Prepare parameters
        params = (prompt_category, prompt_label)

        # Use our context manager to get a connection
        with get_connection() as cursor:
            # Execute the query
            cursor.execute(query, params)

            # Fetch the result
            result = cursor.fetchone()

            if result:
                prompt_text = result[0]
                logger.info(f"Found prompt for category: {prompt_category}, label: {prompt_label}")
                return prompt_text
            else:
                logger.warning(f"No prompt found for category: {prompt_category}, label: {prompt_label}")
                return None
    except Exception as e:
        logger.error(f"Error getting prompt by category and label: {e}")
        return None


def get_prompt_by_label(prompt_label):
    """
    Get a prompt by its label only (ignoring category)

    Args:
        prompt_label (str): The label of the prompt

    Returns:
        str: The prompt text if found, None otherwise

    Note:
        If multiple prompts exist with the same label but different categories,
        this will return the one with the highest version number.
    """
    try:
        # Log the request
        logger.info(f"Getting prompt for label: {prompt_label}")

        # Create the SQL query
        query = f"""
            SELECT prompt
            FROM {CONTENT_SCHEMA}.gptprompts
            WHERE prompt_label = %s
            ORDER BY version DESC
            LIMIT 1
        """

        # Prepare parameters
        params = (prompt_label,)

        # Use our context manager to get a connection
        with get_connection() as cursor:
            # Execute the query
            cursor.execute(query, params)

            # Fetch the result
            result = cursor.fetchone()

            if result:
                prompt_text = result[0]
                logger.info(f"Found prompt for label: {prompt_label}")
                return prompt_text
            else:
                logger.warning(f"No prompt found for label: {prompt_label}")
                return None
    except Exception as e:
        logger.error(f"Error getting prompt by label: {e}")
        return None