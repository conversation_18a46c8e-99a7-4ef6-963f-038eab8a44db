"""
Database access functions for the PYQs Admin module.
"""

import logging
import os
from db_config.db import get_engine, CONTENT_SCHEMA, SHOP_SCHEMA
from contextlib import contextmanager
from datetime import datetime

logger = logging.getLogger(__name__)

@contextmanager
def get_connection(schema):
    """
    Context manager to get a connection from the engine.
    This ensures the connection is properly closed after use.
    """
    # Get the engine
    engine = get_engine(schema)

    # Create a new connection
    connection = engine.raw_connection()
    try:
        yield connection.cursor()
        connection.commit()
    except Exception as e:
        connection.rollback()
        raise e
    finally:
        connection.close()

# Level functions
def get_levels_by_site_id(site_id=1):
    """
    Get all levels for a specific site_id

    Args:
        site_id (int): The site ID to filter by

    Returns:
        list: List of level dictionaries
    """
    try:
        query = """
            SELECT id, name
            FROM wsshop.levels_mst
            WHERE site_id = %s
            ORDER BY sort_by
        """

        with get_connection(SHOP_SCHEMA) as cursor:
            cursor.execute(query, (site_id,))
            rows = cursor.fetchall()

            # Get column names
            columns = [col[0] for col in cursor.description]

            # Convert to list of dictionaries
            levels = [dict(zip(columns, row)) for row in rows]

            return levels
    except Exception as e:
        logger.error(f"Error getting levels: {e}")
        return []

# Syllabus functions
def get_syllabus_by_level(level):
    """
    Get all syllabi for a specific level

    Args:
        level (str): The level name

    Returns:
        list: List of syllabus dictionaries
    """
    try:
        query = """
            SELECT id, syllabus
            FROM wsshop.level_syllabus
            WHERE level = %s
            ORDER BY syllabus
        """

        with get_connection(SHOP_SCHEMA) as cursor:
            cursor.execute(query, (level,))
            rows = cursor.fetchall()

            # Get column names
            columns = [col[0] for col in cursor.description]

            # Convert to list of dictionaries
            syllabi = [dict(zip(columns, row)) for row in rows]

            return syllabi
    except Exception as e:
        logger.error(f"Error getting syllabi: {e}")
        return []

# Grade functions
def get_grades_by_syllabus(syllabus):
    """
    Get all grades for a specific syllabus

    Args:
        syllabus (str): The syllabus name

    Returns:
        list: List of grade dictionaries
    """
    try:
        query = """
            SELECT id, grade
            FROM wsshop.syllabus_grade_dtl
            WHERE syllabus = %s
            ORDER BY sort, grade
        """

        with get_connection(SHOP_SCHEMA) as cursor:
            cursor.execute(query, (syllabus,))
            rows = cursor.fetchall()

            # Get column names
            columns = [col[0] for col in cursor.description]

            # Convert to list of dictionaries
            grades = [dict(zip(columns, row)) for row in rows]

            return grades
    except Exception as e:
        logger.error(f"Error getting grades: {e}")
        return []

# Subject functions
def get_subjects_by_syllabus(syllabus):
    """
    Get all subjects for a specific syllabus

    Args:
        syllabus (str): The syllabus name

    Returns:
        list: List of subject dictionaries
    """
    try:
        query = """
            SELECT id, subject
            FROM wsshop.syllabus_subject
            WHERE syllabus = %s
            ORDER BY subject
        """

        with get_connection(SHOP_SCHEMA) as cursor:
            cursor.execute(query, (syllabus,))
            rows = cursor.fetchall()

            # Get column names
            columns = [col[0] for col in cursor.description]

            # Convert to list of dictionaries
            subjects = [dict(zip(columns, row)) for row in rows]

            return subjects
    except Exception as e:
        logger.error(f"Error getting subjects: {e}")
        return []

# Exam functions
def get_all_exams(limit=10, offset=0, search=None):
    """
    Get all exams with pagination and optional search

    Args:
        limit (int): Number of exams to return
        offset (int): Offset for pagination
        search (str): Optional search term

    Returns:
        tuple: (list of exams, total count)
    """
    try:
        # Base query
        query = f"""
            SELECT id, exam_name, level, syllabus, grade, subject
            FROM {CONTENT_SCHEMA}.content_exam_mst
        """

        # Count query
        count_query = f"""
            SELECT COUNT(*)
            FROM {CONTENT_SCHEMA}.content_exam_mst
        """

        # Add search condition if provided
        params = []
        if search:
            search_term = f"%{search}%"
            query += """
                WHERE exam_name LIKE %s
                OR level LIKE %s
                OR syllabus LIKE %s
                OR grade LIKE %s
                OR subject LIKE %s
            """
            count_query += """
                WHERE exam_name LIKE %s
                OR level LIKE %s
                OR syllabus LIKE %s
                OR grade LIKE %s
                OR subject LIKE %s
            """
            params = [search_term, search_term, search_term, search_term, search_term]

        # Add order and limit
        query += """
            ORDER BY id DESC
            LIMIT %s OFFSET %s
        """

        # Add limit and offset parameters
        params_with_limit = params + [limit, offset]

        with get_connection(CONTENT_SCHEMA) as cursor:
            # Get total count
            cursor.execute(count_query, params)
            total_count = cursor.fetchone()[0]

            # Get exams
            cursor.execute(query, params_with_limit)
            rows = cursor.fetchall()

            # Get column names
            columns = [col[0] for col in cursor.description]

            # Convert to list of dictionaries
            exams = [dict(zip(columns, row)) for row in rows]

            return exams, total_count
    except Exception as e:
        logger.error(f"Error getting exams: {e}")
        return [], 0

def get_exam_by_id(exam_id):
    """
    Get an exam by its ID

    Args:
        exam_id (int): The ID of the exam

    Returns:
        dict: The exam data
    """
    try:
        query = f"""
            SELECT id, exam_name, level, syllabus, grade, subject, syllabus_text
            FROM {CONTENT_SCHEMA}.content_exam_mst
            WHERE id = %s
        """

        with get_connection(CONTENT_SCHEMA) as cursor:
            cursor.execute(query, (exam_id,))
            row = cursor.fetchone()

            if row:
                # Get column names
                columns = [col[0] for col in cursor.description]

                # Convert to dictionary
                exam_data = dict(zip(columns, row))

                return exam_data
            return None
    except Exception as e:
        logger.error(f"Error getting exam by ID: {e}")
        return None

def check_exam_exists(exam_data):
    """
    Check if an exam with the same name and category combination already exists

    Args:
        exam_data (dict): The exam data containing name, level, syllabus, grade, and subject

    Returns:
        bool: True if a duplicate exists, False otherwise
    """
    try:
        query = f"""
            SELECT COUNT(*) FROM {CONTENT_SCHEMA}.content_exam_mst
            WHERE exam_name = %s
            AND level = %s
            AND syllabus = %s
            AND grade = %s
            AND subject = %s
        """

        params = (
            exam_data["exam_name"],
            exam_data["level"],
            exam_data["syllabus"],
            exam_data["grade"],
            exam_data["subject"]
        )

        with get_connection(CONTENT_SCHEMA) as cursor:
            cursor.execute(query, params)
            count = cursor.fetchone()[0]

            return count > 0
    except Exception as e:
        logger.error(f"Error checking if exam exists: {e}")
        raise

def create_exam(exam_data, username):
    """
    Create a new exam

    Args:
        exam_data (dict): The exam data
        username (str): The username of the creator

    Returns:
        int: The ID of the newly created exam

    Raises:
        ValueError: If an exam with the same name and category combination already exists
    """
    try:
        # Check if exam already exists
        if check_exam_exists(exam_data):
            raise ValueError("An exam with the same name and category combination already exists")

        query = f"""
            INSERT INTO {CONTENT_SCHEMA}.content_exam_mst
            (version, created_by, date_created, exam_name, level, syllabus, grade, subject, syllabus_text)
            VALUES
            (0, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        params = (
            username,
            datetime.now(),
            exam_data["exam_name"],
            exam_data["level"],
            exam_data["syllabus"],
            exam_data["grade"],
            exam_data["subject"],
            exam_data.get("syllabus_text", "")
        )

        with get_connection(CONTENT_SCHEMA) as cursor:
            cursor.execute(query, params)

            # Get the ID of the newly created exam
            cursor.execute("SELECT LAST_INSERT_ID() as id")
            last_id = cursor.fetchone()[0]

            return last_id
    except ValueError as e:
        # Re-raise ValueError for duplicate exams
        logger.warning(f"Duplicate exam: {e}")
        raise
    except Exception as e:
        logger.error(f"Error creating exam: {e}")
        raise

def update_exam(exam_id, exam_data):
    """
    Update an existing exam

    Args:
        exam_id (int): The ID of the exam to update
        exam_data (dict): The updated exam data

    Returns:
        bool: True if the update was successful, False otherwise

    Raises:
        ValueError: If an exam with the same name and category combination already exists
    """
    try:
        # Check if another exam with the same details exists (excluding this exam)
        query = f"""
            SELECT COUNT(*) FROM {CONTENT_SCHEMA}.content_exam_mst
            WHERE exam_name = %s
            AND level = %s
            AND syllabus = %s
            AND grade = %s
            AND subject = %s
            AND id != %s
        """

        params = (
            exam_data["exam_name"],
            exam_data["level"],
            exam_data["syllabus"],
            exam_data["grade"],
            exam_data["subject"],
            exam_id
        )

        with get_connection(CONTENT_SCHEMA) as cursor:
            cursor.execute(query, params)
            count = cursor.fetchone()[0]

            if count > 0:
                raise ValueError("An exam with the same name and category combination already exists")

            # Update the exam
            update_query = f"""
                UPDATE {CONTENT_SCHEMA}.content_exam_mst
                SET exam_name = %s,
                    level = %s,
                    syllabus = %s,
                    grade = %s,
                    subject = %s,
                    syllabus_text = %s,
                    version = version + 1
                WHERE id = %s
            """

            update_params = (
                exam_data["exam_name"],
                exam_data["level"],
                exam_data["syllabus"],
                exam_data["grade"],
                exam_data["subject"],
                exam_data.get("syllabus_text", ""),
                exam_id
            )

            cursor.execute(update_query, update_params)
            rows_affected = cursor.rowcount

            return rows_affected > 0
    except ValueError as e:
        # Re-raise ValueError for duplicate exams
        logger.warning(f"Duplicate exam: {e}")
        raise
    except Exception as e:
        logger.error(f"Error updating exam: {e}")
        raise


# Exam Document functions
def get_exam_documents(exam_id, limit=10, offset=0):
    """
    Get all documents for a specific exam with pagination

    Args:
        exam_id (int): The ID of the exam
        limit (int): Number of documents to return
        offset (int): Offset for pagination

    Returns:
        tuple: (list of documents, total count)
    """
    try:
        # Base query
        query = f"""
            SELECT d.id, d.exam_id, d.year, d.month, d.shift, d.question_paper_path,
                   (d.extracted_content IS NOT NULL) as has_extracted_content,
                   (EXISTS (SELECT 1 FROM {CONTENT_SCHEMA}.content_exam_solutions s WHERE s.exam_dtl_id = d.id)) as has_solutions,
                   d.date_created, d.created_by
            FROM {CONTENT_SCHEMA}.content_exam_dtl d
            WHERE d.exam_id = %s
        """

        # Count query
        count_query = f"""
            SELECT COUNT(*)
            FROM {CONTENT_SCHEMA}.content_exam_dtl
            WHERE exam_id = %s
        """

        # Add order and limit
        query += """
            ORDER BY year DESC, month DESC, shift DESC
            LIMIT %s OFFSET %s
        """

        with get_connection(CONTENT_SCHEMA) as cursor:
            # Get total count
            cursor.execute(count_query, (exam_id,))
            total_count = cursor.fetchone()[0]

            # Get documents
            cursor.execute(query, (exam_id, limit, offset))
            rows = cursor.fetchall()

            # Get column names
            columns = [col[0] for col in cursor.description]

            # Convert to list of dictionaries
            documents = [dict(zip(columns, row)) for row in rows]

            return documents, total_count
    except Exception as e:
        logger.error(f"Error getting exam documents: {e}")
        return [], 0

def get_exam_document_by_id(document_id):
    """
    Get an exam document by its ID

    Args:
        document_id (int): The ID of the document

    Returns:
        dict: The document data
    """
    try:
        query = f"""
            SELECT id, exam_id, year, month, shift, question_paper_path, extracted_content,
                   date_created, created_by
            FROM {CONTENT_SCHEMA}.content_exam_dtl
            WHERE id = %s
        """

        with get_connection(CONTENT_SCHEMA) as cursor:
            cursor.execute(query, (document_id,))
            row = cursor.fetchone()

            if row:
                # Get column names
                columns = [col[0] for col in cursor.description]

                # Convert to dictionary
                document_data = dict(zip(columns, row))

                return document_data
            return None
    except Exception as e:
        logger.error(f"Error getting exam document by ID: {e}")
        return None

def create_exam_document(exam_id, document_data, username):
    """
    Create a new exam document

    Args:
        exam_id (int): The ID of the exam
        document_data (dict): The document data
        username (str): The username of the creator

    Returns:
        int: The ID of the newly created document
    """
    try:
        query = f"""
            INSERT INTO {CONTENT_SCHEMA}.content_exam_dtl
            (exam_id, year, month, shift, question_paper_path, extracted_content, created_by, date_created, version)
            VALUES
            (%s, %s, %s, %s, %s, NULL, %s, %s, 0)
        """

        params = (
            exam_id,
            document_data["year"],
            document_data.get("month", ""),
            document_data.get("shift", ""),
            document_data["question_paper_path"],
            username,
            datetime.now()
        )

        with get_connection(CONTENT_SCHEMA) as cursor:
            cursor.execute(query, params)

            # Get the ID of the newly created document
            cursor.execute("SELECT LAST_INSERT_ID() as id")
            last_id = cursor.fetchone()[0]

            return last_id
    except Exception as e:
        logger.error(f"Error creating exam document: {e}")
        raise

def update_exam_document_content(document_id, extracted_content):
    """
    Update the extracted content of an exam document

    Args:
        document_id (int): The ID of the document
        extracted_content (str): The extracted HTML content

    Returns:
        bool: True if the update was successful, False otherwise
    """
    try:
        query = f"""
            UPDATE {CONTENT_SCHEMA}.content_exam_dtl
            SET extracted_content = %s,
                version = version + 1
            WHERE id = %s
        """

        with get_connection(CONTENT_SCHEMA) as cursor:
            cursor.execute(query, (extracted_content, document_id))
            rows_affected = cursor.rowcount

            return rows_affected > 0
    except Exception as e:
        logger.error(f"Error updating exam document content: {e}")
        raise

# Exam Solutions functions
def get_exam_solutions(exam_dtl_id, limit=100, offset=0):
    """
    Get all solutions for a specific exam document with pagination

    Args:
        exam_dtl_id (int): The ID of the exam document
        limit (int): Number of solutions to return
        offset (int): Offset for pagination

    Returns:
        tuple: (list of solutions, total count)
    """
    try:
        # Base query
        query = f"""
            SELECT id, exam_dtl_id, question, question_type,
                   option1, option2, option3, option4, option5, answer,
                   marks, negative_mark, topic, subtopic
            FROM {CONTENT_SCHEMA}.content_exam_solutions
            WHERE exam_dtl_id = %s
        """

        # Count query
        count_query = f"""
            SELECT COUNT(*)
            FROM {CONTENT_SCHEMA}.content_exam_solutions
            WHERE exam_dtl_id = %s
        """

        # Add order and limit
        query += """
            ORDER BY id desc
            LIMIT %s OFFSET %s
        """

        with get_connection(CONTENT_SCHEMA) as cursor:
            # Get total count
            cursor.execute(count_query, (exam_dtl_id,))
            total_count = cursor.fetchone()[0]

            # Get solutions
            cursor.execute(query, (exam_dtl_id, limit, offset))
            rows = cursor.fetchall()

            # Get column names
            columns = [col[0] for col in cursor.description]

            # Convert to list of dictionaries
            solutions = [dict(zip(columns, row)) for row in rows]

            return solutions, total_count
    except Exception as e:
        logger.error(f"Error getting exam solutions: {e}")
        return [], 0

def create_exam_solution(solution_data, username):
    """
    Create a new exam solution

    Args:
        solution_data (dict): The solution data
        username (str): The username of the creator

    Returns:
        int: The ID of the newly created solution
    """
    try:
        query = f"""
            INSERT INTO {CONTENT_SCHEMA}.content_exam_solutions
            (exam_dtl_id, question, question_type,
             option1, option2, option3, option4, option5, answer,
             marks, negative_mark, topic, subtopic,
             version)
            VALUES
            (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0)
        """

        params = (
            solution_data["exam_dtl_id"],
            solution_data["question"],
            solution_data.get("question_type", ""),
            solution_data.get("option1", ""),
            solution_data.get("option2", ""),
            solution_data.get("option3", ""),
            solution_data.get("option4", ""),
            solution_data.get("option5", ""),
            solution_data.get("answer", ""),
            solution_data.get("marks", None),
            solution_data.get("negative_mark", None),
            solution_data.get("topic", ""),
            solution_data.get("subtopic", "")
        )

        with get_connection(CONTENT_SCHEMA) as cursor:
            cursor.execute(query, params)

            # Get the ID of the newly created solution
            cursor.execute("SELECT LAST_INSERT_ID() as id")
            last_id = cursor.fetchone()[0]

            return last_id
    except Exception as e:
        logger.error(f"Error creating exam solution: {e}")
        raise

def update_exam_solution(solution_id, solution_data):
    """
    Update an existing exam solution

    Args:
        solution_id (int): The ID of the solution to update
        solution_data (dict): The updated solution data

    Returns:
        bool: True if the update was successful, False otherwise
    """
    try:
        query = f"""
            UPDATE {CONTENT_SCHEMA}.content_exam_solutions
            SET question = %s,
                question_type = %s,
                option1 = %s,
                option2 = %s,
                option3 = %s,
                option4 = %s,
                option5 = %s,
                answer = %s,
                marks = %s,
                negative_mark = %s,
                topic = %s,
                subtopic = %s,
                version = version + 1
            WHERE id = %s
        """

        params = (
            solution_data["question"],
            solution_data.get("question_type", ""),
            solution_data.get("option1", ""),
            solution_data.get("option2", ""),
            solution_data.get("option3", ""),
            solution_data.get("option4", ""),
            solution_data.get("option5", ""),
            solution_data.get("answer", ""),
            solution_data.get("marks", None),
            solution_data.get("negative_mark", None),
            solution_data.get("topic", ""),
            solution_data.get("subtopic", ""),
            solution_id
        )

        with get_connection(CONTENT_SCHEMA) as cursor:
            cursor.execute(query, params)
            rows_affected = cursor.rowcount

            return rows_affected > 0
    except Exception as e:
        logger.error(f"Error updating exam solution: {e}")
        raise

def delete_exam_solution(solution_id):
    """
    Delete an exam solution

    Args:
        solution_id (int): The ID of the solution to delete

    Returns:
        bool: True if the deletion was successful, False otherwise
    """
    try:
        query = f"""
            DELETE FROM {CONTENT_SCHEMA}.content_exam_solutions
            WHERE id = %s
        """

        with get_connection(CONTENT_SCHEMA) as cursor:
            cursor.execute(query, (solution_id,))
            rows_affected = cursor.rowcount

            return rows_affected > 0
    except Exception as e:
        logger.error(f"Error deleting exam solution: {e}")
        raise

def delete_all_exam_solutions(exam_dtl_id):
    """
    Delete all solutions for a specific exam document

    Args:
        exam_dtl_id (int): The ID of the exam document

    Returns:
        bool: True if the deletion was successful, False otherwise
    """
    try:
        query = f"""
            DELETE FROM {CONTENT_SCHEMA}.content_exam_solutions
            WHERE exam_dtl_id = %s
        """

        with get_connection(CONTENT_SCHEMA) as cursor:
            cursor.execute(query, (exam_dtl_id,))
            rows_affected = cursor.rowcount

            return rows_affected > 0
    except Exception as e:
        logger.error(f"Error deleting all exam solutions: {e}")
        raise

def remove_exam_document(document_id):
    """
    Delete an exam document

    Args:
        document_id (int): The ID of the document to delete

    Returns:
        bool: True if the deletion was successful, False otherwise
    """
    try:
        query = f"""
            DELETE FROM {CONTENT_SCHEMA}.content_exam_dtl
            WHERE id = %s
        """

        with get_connection(CONTENT_SCHEMA) as cursor:
            cursor.execute(query, (document_id,))
            rows_affected = cursor.rowcount

            return rows_affected > 0
    except Exception as e:
        logger.error(f"Error deleting exam document: {e}")
        return False
