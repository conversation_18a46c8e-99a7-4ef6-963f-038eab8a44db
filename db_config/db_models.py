from sqlalchemy import <PERSON>um<PERSON>, <PERSON>te<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ForeignKey, Table
from sqlalchemy.orm import relationship
from db_config.db import Base, AUTH_SCHEMA

class User(Base):
    __tablename__ = "user"
    __table_args__ = {"schema": AUTH_SCHEMA}

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(255))
    email = Column(String(255))
    mobile = Column(String(255))
    password = Column(String(255))
    enabled = Column(Boolean)
    site_id = Column(Integer)

    # Relationship to user_role
    roles = relationship("UserRole", back_populates="user")

    def has_role(self, role_name):
        """Check if user has a specific role"""
        return any(user_role.role.authority == role_name for user_role in self.roles)

class Role(Base):
    __tablename__ = "role"
    __table_args__ = {"schema": AUTH_SCHEMA}

    id = Column(Integer, primary_key=True, index=True)
    version = Column(Integer, default=0)
    authority = Column(String(255))

    # Relationship to user_role
    users = relationship("UserRole", back_populates="role")

class UserRole(Base):
    __tablename__ = "user_role"
    __table_args__ = {"schema": AUTH_SCHEMA}

    # Define composite primary key since there's no id column
    user_id = Column(Integer, ForeignKey(f"{AUTH_SCHEMA}.user.id"), primary_key=True)
    role_id = Column(Integer, ForeignKey(f"{AUTH_SCHEMA}.role.id"), primary_key=True)

    # Relationships
    user = relationship("User", back_populates="roles")
    role = relationship("Role", back_populates="users")
